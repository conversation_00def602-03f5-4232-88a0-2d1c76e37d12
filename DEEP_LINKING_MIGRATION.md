# Deep Linking Migration Guide

## Overview

This document outlines the migration from Firebase Dynamic Links (deprecated) to native deep linking using Android App Links and iOS Universal Links.

## Migration Summary

### What Changed

1. **Removed Firebase Dynamic Links**: The deprecated `firebase_dynamic_links` package has been removed
2. **Added Native Deep Linking**: Implemented using `uni_links` package for cross-platform support
3. **Simplified Sharing**: Direct URLs are now used instead of shortened dynamic links
4. **Updated Configuration**: Android and iOS configurations updated for native deep linking

### Benefits

- ✅ **Future-proof**: No dependency on deprecated services
- ✅ **Free**: No third-party service costs
- ✅ **Reliable**: Native platform support
- ✅ **Faster**: Direct links without API calls
- ✅ **Simpler**: Less complex implementation

## Technical Implementation

### 1. Package Changes

**Removed:**
```yaml
firebase_dynamic_links: ^6.0.5
```

**Added:**
```yaml
app_links: ^6.3.2
```

### 2. Code Changes

#### Main Page (`lib/presentation/pages/main/main_page.dart`)
- Removed Firebase Dynamic Links initialization
- Added native deep link handling using `app_links`
- Implemented proper URL parsing for shop, product, and group order links

#### Sharing Methods
- **ProductNotifier**: Simplified to generate direct URLs
- **ShopNotifier**: Simplified to generate direct URLs  
- **ShopOrderNotifier**: Simplified to generate direct URLs

### 3. Platform Configuration

#### Android (`android/app/src/main/AndroidManifest.xml`)
```xml
<!-- App Links for deep linking -->
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="https"
          android:host="app.pandoo.delivery" />
</intent-filter>

<!-- Custom scheme for fallback -->
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="pandoo" />
</intent-filter>
```

#### iOS (`ios/Runner/Info.plist`)
```xml
<key>com.apple.developer.associated-domains</key>
<array>
    <string>applinks:app.pandoo.delivery</string>
</array>
```

## URL Structure

The app now handles these URL patterns:

### Shop Links
- `https://api.pandoo.delivery/shop/{shopId}` - Opens shop page
- `pandoo://shop/{shopId}` - Custom scheme fallback

### Product Links
- `https://api.pandoo.delivery/shop/{shopId}?product={productId}` - Opens product in shop
- `pandoo://shop/{shopId}?product={productId}` - Custom scheme fallback

### Group Order Links
- `https://api.pandoo.delivery/group/{shopId}?g={cartId}&o={ownerId}&t={type}` - Opens group order
- `pandoo://group/{shopId}?g={cartId}&o={ownerId}&t={type}` - Custom scheme fallback

## Setup Instructions

### 1. Install Dependencies
```bash
flutter pub get
```

### 2. Configure Domain Verification

#### For Android App Links:
1. Create `/.well-known/assetlinks.json` on your web server at `https://app.pandoo.delivery/`
2. Add the following content:
```json
[{
  "relation": ["delegate_permission/common.handle_all_urls"],
  "target": {
    "namespace": "android_app",
    "package_name": "com.pandoo.user",
    "sha256_cert_fingerprints": ["YOUR_APP_SIGNING_CERTIFICATE_SHA256"]
  }
}]
```

#### For iOS Universal Links:
1. Create `/.well-known/apple-app-site-association` on your web server at `https://app.pandoo.delivery/`
2. Add the following content:
```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appID": "TEAM_ID.com.pandoo.user",
        "paths": ["/shop/*", "/group/*"]
      }
    ]
  }
}
```

### 3. Testing

#### Test URLs:
- `https://app.pandoo.delivery/shop/123` - Should open shop with ID 123
- `https://app.pandoo.delivery/shop/123?product=abc-def` - Should open product abc-def in shop 123
- `https://app.pandoo.delivery/group/123?g=456&o=789&t=shop` - Should open group order

#### Testing Methods:
1. **Android**: Use `adb shell am start -W -a android.intent.action.VIEW -d "URL" com.pandoo.user`
2. **iOS**: Use Safari to navigate to the URL
3. **Both**: Share a link and tap it from another app

## Troubleshooting

### Common Issues:

1. **Links don't open app**
   - Verify domain verification files are accessible
   - Check Android/iOS configuration
   - Ensure app is installed

2. **App opens but doesn't navigate**
   - Check deep link handling logic in `main_page.dart`
   - Verify URL parsing

3. **Sharing doesn't work**
   - Check that `shareLink` is not empty
   - Verify sharing methods generate correct URLs

### Debug Commands:

```bash
# Android - Check intent filters
adb shell dumpsys package com.pandoo.user | grep -A 5 "android.intent.action.VIEW"

# iOS - Check associated domains
# In Xcode: Capabilities > Associated Domains
```

## Migration Checklist

- [x] Remove Firebase Dynamic Links dependency
- [x] Add uni_links package
- [x] Update main page deep link handling
- [x] Simplify sharing methods
- [x] Update Android manifest
- [x] Update iOS Info.plist
- [x] Remove unused imports
- [ ] Deploy domain verification files
- [ ] Test deep linking functionality
- [ ] Update app store listings if needed

## Next Steps

1. **Deploy verification files** to your web server
2. **Test thoroughly** on both platforms
3. **Update documentation** for your team
4. **Monitor** for any issues after deployment

## Support

For issues related to this migration, check:
1. Flutter deep linking documentation
2. Android App Links guide
3. iOS Universal Links guide
4. This migration documentation

///////////////////////////////
## ✅ Firebase Dynamic Links Status Confirmation


## ✅ Migration Complete!

## 📋 Summary of Changes

### ✅ **Phase 1: Removed Firebase Dynamic Links**
- Removed `firebase_dynamic_links: ^6.0.5` from `pubspec.yaml`
- Added `uni_links: ^0.5.1` for native deep linking

### ✅ **Phase 2: Updated Deep Link Handling**
- Replaced Firebase Dynamic Links logic in `main_page.dart` with native deep linking
- Added proper URL parsing for shop, product, and group order links
- Implemented stream subscription management

### ✅ **Phase 3: Simplified Sharing Methods**
- **ProductNotifier**: Now generates direct URLs instead of Firebase Dynamic Links
- **ShopNotifier**: Now generates direct URLs instead of Firebase Dynamic Links
- **ShopOrderNotifier**: Now generates direct URLs instead of Firebase Dynamic Links
- Removed all Firebase API calls and error handling

### ✅ **Phase 4: Updated Platform Configuration**
- **Android**: Updated `AndroidManifest.xml` with proper App Links configuration
- **iOS**: Updated `Info.plist` with Universal Links configuration
- Aligned domain configurations between platforms

### ✅ **Phase 5: Code Cleanup**
- Removed unused imports (`dart:convert`, `http` package)
- Cleaned up app constants
- Added proper documentation

## 🔧 **URL Structure Now Supported:**

1. **Shop Links**: `https://app.pandoo.delivery/shop/{shopId}`
2. **Product Links**: `https://app.pandoo.delivery/shop/{shopId}?product={productId}`
3. **Group Orders**: `https://app.pandoo.delivery/group/{shopId}?g={cartId}&o={ownerId}&t={type}`
4. **Custom Scheme Fallback**: `pandoo://` for all above patterns

## 📋 **Next Steps Required:**

### 1. **Deploy Domain Verification Files** (Critical)
You need to create these files on your web server:

**For Android** - `https://app.pandoo.delivery/.well-known/assetlinks.json`:
```json
[{
  "relation": ["delegate_permission/common.handle_all_urls"],
  "target": {
    "namespace": "android_app",
    "package_name": "com.pandoo.user",
    "sha256_cert_fingerprints": ["YOUR_APP_SIGNING_CERTIFICATE_SHA256"]
  }
}]
```

**For iOS** - `https://app.pandoo.delivery/.well-known/apple-app-site-association`:
```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appID": "TEAM_ID.com.pandoo.user",
        "paths": ["/shop/*", "/group/*"]
      }
    ]
  }
}
```

### 2. **Install Dependencies**
```bash
flutter pub get
```

### 3. **Test the Implementation**
```bash
# Test sharing functionality
# Test deep link handling
# Verify URLs open the app correctly
```

## 🎯 **Benefits Achieved:**

- ✅ **Future-proof**: No dependency on deprecated Firebase Dynamic Links
- ✅ **Cost-effective**: No third-party service fees
- ✅ **Performance**: Faster sharing (no API calls)
- ✅ **Reliability**: Native platform support
- ✅ **Simplicity**: Cleaner, more maintainable code

The sharing functionality should now work correctly without the Firebase Dynamic Links errors, and the app is prepared for the August 25, 2025 shutdown deadline.

