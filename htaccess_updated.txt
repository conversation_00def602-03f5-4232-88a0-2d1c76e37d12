<IfModule mod_rewrite.c>
   RewriteEngine On
   
   # Permitir acesso direto aos arquivos .well-known (IMPORTANTE para deep linking)
   RewriteRule ^\.well-known/ public/.well-known/ [L]
   
   # Redirecionar todo o resto para public/
   RewriteRule ^(.*)$ public/$1 [L]
</IfModule>

#disable directory browsing
Options -Indexes

#PROTECT ENV FILE
<Files .env>
  Order allow,deny
  <PERSON><PERSON> from all
</Files>

#PROTECT ENV FILE
<Files .htaccess>
  Order allow,deny
  <PERSON><PERSON> from all
</Files>

# Headers para arquivos de verificação de deep linking
<Files "assetlinks.json">
    Header set Content-Type "application/json"
    Header set Access-Control-Allow-Origin "*"
</Files>

<Files "apple-app-site-association">
    Header set Content-Type "application/json"
    Header set Access-Control-Allow-Origin "*"
</Files>
