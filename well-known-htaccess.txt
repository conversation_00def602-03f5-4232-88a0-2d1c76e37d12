# .htaccess para pasta .well-known
# Coloque este arquivo em: public/.well-known/.htaccess

# Permitir acesso a todos os arquivos desta pasta
<Files "*">
    Order allow,deny
    Allow from all
</Files>

# Configurar Content-Type correto para arquivos de verificação
<Files "assetlinks.json">
    Header set Content-Type "application/json"
    Header set Access-Control-Allow-Origin "*"
</Files>

<Files "apple-app-site-association">
    Header set Content-Type "application/json"
    Header set Access-Control-Allow-Origin "*"
</Files>

# Desabilitar cache para estes arquivos durante desenvolvimento
<IfModule mod_headers.c>
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</IfModule>
