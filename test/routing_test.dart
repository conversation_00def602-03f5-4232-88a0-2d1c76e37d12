import 'package:flutter_test/flutter_test.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:pandoo_delivery/app_constants.dart';

void main() {
  group('OpenRouteService API Tests', () {
    test('should format coordinates correctly for API request', () {
      // Arrange
      const start = LatLng(-23.5505, -46.6333); // São Paulo
      const end = LatLng(-22.9068, -43.1729);   // Rio de Janeiro
      
      // Act
      final startParam = "${start.longitude},${start.latitude}";
      final endParam = "${end.longitude},${end.latitude}";
      
      // Assert
      expect(startParam, equals("-46.6333,-23.5505"));
      expect(endParam, equals("-43.1729,-22.9068"));
      
      // Verify no parentheses in the format
      expect(startParam, isNot(contains('(')));
      expect(startParam, isNot(contains(')')));
      expect(endParam, isNot(contains('(')));
      expect(endParam, isNot(contains(')')));
    });
    
    test('should have valid Brazilian demo coordinates', () {
      // Assert
      expect(AppConstants.demoLatitude, equals(-23.5505));
      expect(AppConstants.demoLongitude, equals(-46.6333));
      
      // Verify coordinates are in Brazil (rough bounds check)
      expect(AppConstants.demoLatitude, greaterThan(-35.0)); // Southern Brazil
      expect(AppConstants.demoLatitude, lessThan(5.0));      // Northern Brazil
      expect(AppConstants.demoLongitude, greaterThan(-75.0)); // Western Brazil
      expect(AppConstants.demoLongitude, lessThan(-30.0));    // Eastern Brazil
    });
    
    test('should have valid OpenRouteService API key format', () {
      // Assert
      expect(AppConstants.routingKey, isNotEmpty);
      expect(AppConstants.routingKey.length, equals(56)); // Actual ORS key length
      expect(AppConstants.routingKey, matches(RegExp(r'^[a-f0-9]+$'))); // Hex format
    });
    
    test('should construct valid API URL parameters', () {
      // Arrange
      const start = LatLng(-23.5505, -46.6333);
      const end = LatLng(-22.9068, -43.1729);
      
      // Act
      final queryParams = {
        "api_key": AppConstants.routingKey,
        "start": "${start.longitude},${start.latitude}",
        "end": "${end.longitude},${end.latitude}"
      };
      
      // Assert
      expect(queryParams["api_key"], equals(AppConstants.routingKey));
      expect(queryParams["start"], equals("-46.6333,-23.5505"));
      expect(queryParams["end"], equals("-43.1729,-22.9068"));
      
      // Verify URL encoding won't add unwanted characters
      final startEncoded = Uri.encodeQueryComponent(queryParams["start"]!);
      final endEncoded = Uri.encodeQueryComponent(queryParams["end"]!);
      
      expect(startEncoded, equals("-46.6333%2C-23.5505"));
      expect(endEncoded, equals("-43.1729%2C-22.9068"));
    });
  });
}
