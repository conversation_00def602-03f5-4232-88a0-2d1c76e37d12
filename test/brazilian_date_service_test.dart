import 'package:flutter_test/flutter_test.dart';
import 'package:pandoo_delivery/infrastructure/services/brazilian_date_service.dart';

void main() {
  group('BrazilianDateService.formatRelativeTime', () {
    test('should format recent times in Portuguese', () {
      final now = DateTime.now();
      
      // Test "agora mesmo" (just now)
      final justNow = now.subtract(const Duration(seconds: 30));
      expect(BrazilianDateService.formatRelativeTime(justNow), contains('agora mesmo'));
      
      // Test minutes
      final oneMinuteAgo = now.subtract(const Duration(minutes: 1));
      expect(BrazilianDateService.formatRelativeTime(oneMinuteAgo), contains('há 1 minuto'));
      
      final fiveMinutesAgo = now.subtract(const Duration(minutes: 5));
      expect(BrazilianDateService.formatRelativeTime(fiveMinutesAgo), contains('há 5 minutos'));
      
      // Test hours
      final oneHourAgo = now.subtract(const Duration(hours: 1));
      expect(BrazilianDateService.formatRelativeTime(oneHourAgo), contains('há 1 hora'));
      
      final threeHoursAgo = now.subtract(const Duration(hours: 3));
      expect(BrazilianDateService.formatRelativeTime(threeHoursAgo), contains('há 3 horas'));
      
      // Test days
      final oneDayAgo = now.subtract(const Duration(days: 1));
      expect(BrazilianDateService.formatRelativeTime(oneDayAgo), contains('há 1 dia'));
      
      final threeDaysAgo = now.subtract(const Duration(days: 3));
      expect(BrazilianDateService.formatRelativeTime(threeDaysAgo), contains('há 3 dias'));
      
      // Test weeks
      final oneWeekAgo = now.subtract(const Duration(days: 7));
      expect(BrazilianDateService.formatRelativeTime(oneWeekAgo), contains('há 1 semana'));
      
      final twoWeeksAgo = now.subtract(const Duration(days: 14));
      expect(BrazilianDateService.formatRelativeTime(twoWeeksAgo), contains('há 2 semanas'));
    });
    
    test('should handle null dates', () {
      expect(BrazilianDateService.formatRelativeTime(null), equals(''));
    });
    
    test('should handle future dates gracefully', () {
      final future = DateTime.now().add(const Duration(hours: 1));
      final result = BrazilianDateService.formatRelativeTime(future);
      expect(result, contains('agora mesmo'));
    });
    
    test('should format months and years correctly', () {
      final now = DateTime.now();
      
      // Test months
      final oneMonthAgo = now.subtract(const Duration(days: 35));
      expect(BrazilianDateService.formatRelativeTime(oneMonthAgo), contains('há 1 mês'));
      
      final threeMonthsAgo = now.subtract(const Duration(days: 100));
      expect(BrazilianDateService.formatRelativeTime(threeMonthsAgo), contains('há 3 meses'));
      
      // Test years
      final oneYearAgo = now.subtract(const Duration(days: 400));
      expect(BrazilianDateService.formatRelativeTime(oneYearAgo), contains('há 1 ano'));
      
      final twoYearsAgo = now.subtract(const Duration(days: 800));
      expect(BrazilianDateService.formatRelativeTime(twoYearsAgo), contains('há 2 anos'));
    });
  });
}
