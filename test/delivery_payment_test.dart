import 'package:flutter_test/flutter_test.dart';
import 'package:pandoo_delivery/infrastructure/services/brazilian_payment_methods.dart';

void main() {
  group('Brazilian Payment Methods Tests', () {
    test('should return default delivery payment methods', () {
      // Act
      final methods = BrazilianPaymentMethods.getDefaultMethods();
      
      // Assert
      expect(methods, isNotEmpty);
      expect(methods.length, equals(4));
      
      // Check PIX na Entrega
      final pixMethod = methods.firstWhere((m) => m.tag == 'pix_delivery');
      expect(pixMethod.name, equals('PIX na Entrega'));
      expect(pixMethod.icon, equals('📱'));
      expect(pixMethod.supportsChange, isFalse);
      
      // Check Credit Card na Entrega
      final creditMethod = methods.firstWhere((m) => m.tag == 'card_credit_delivery');
      expect(creditMethod.name, equals('Cartão de Crédito na Entrega'));
      expect(creditMethod.icon, equals('💳'));
      expect(creditMethod.supportsChange, isFalse);
      
      // Check Debit Card na Entrega
      final debitMethod = methods.firstWhere((m) => m.tag == 'card_debit_delivery');
      expect(debitMethod.name, equals('Cartão de Débito na Entrega'));
      expect(debitMethod.icon, equals('💳'));
      expect(debitMethod.supportsChange, isFalse);
      
      // Check Cash na Entrega
      final cashMethod = methods.firstWhere((m) => m.tag == 'cash_delivery');
      expect(cashMethod.name, equals('Dinheiro na Entrega'));
      expect(cashMethod.icon, equals('💵'));
      expect(cashMethod.supportsChange, isTrue);
      expect(cashMethod.maxChangeAmount, equals(500.0));
    });
    
    test('should return payment methods response for shop', () {
      // Act
      final response = BrazilianPaymentMethods.getPaymentMethodsForShop(shopId: 123);
      
      // Assert
      expect(response.deliveryPaymentMethods, isNotEmpty);
      expect(response.deliveryPaymentMethods.length, equals(4));
      expect(response.shopId, equals(123));
      expect(response.instructions, isNotEmpty);
      expect(response.instructions, contains('Escolha como você gostaria de pagar na entrega'));
    });
    
    test('should format currency correctly', () {
      // Act & Assert
      expect(BrazilianPaymentMethods.formatCurrency(10.50), equals('R\$ 10,50'));
      expect(BrazilianPaymentMethods.formatCurrency(100.00), equals('R\$ 100,00'));
      expect(BrazilianPaymentMethods.formatCurrency(0.99), equals('R\$ 0,99'));
    });
    
    test('should parse currency correctly', () {
      // Act & Assert
      expect(BrazilianPaymentMethods.parseCurrency('R\$ 10,50'), equals(10.50));
      expect(BrazilianPaymentMethods.parseCurrency('R\$ 100,00'), equals(100.00));
      expect(BrazilianPaymentMethods.parseCurrency('10,50'), equals(10.50));
      expect(BrazilianPaymentMethods.parseCurrency('invalid'), equals(0.0));
    });
  });
}
