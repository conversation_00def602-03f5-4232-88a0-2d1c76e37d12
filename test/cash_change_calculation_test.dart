import 'package:flutter_test/flutter_test.dart';
import 'package:pandoo_delivery/application/delivery_payment/delivery_payment_state.dart';

void main() {
  group('Cash Change Calculation Tests', () {
    test('should calculate change correctly', () {
      const state = DeliveryPaymentState(
        changeRequired: true,
        cashPaymentAmount: 100.0,
        orderTotal: 75.50,
      );

      expect(state.calculatedChange, equals(24.50));
    });

    test('should return 0 change when cash amount equals order total', () {
      const state = DeliveryPaymentState(
        changeRequired: true,
        cashPaymentAmount: 75.50,
        orderTotal: 75.50,
      );

      expect(state.calculatedChange, equals(0.0));
    });

    test('should return 0 change when change not required', () {
      const state = DeliveryPaymentState(
        changeRequired: false,
        cashPaymentAmount: 100.0,
        orderTotal: 75.50,
      );

      expect(state.calculatedChange, equals(0.0));
    });

    test('should validate cash amount correctly', () {
      const validState = DeliveryPaymentState(
        changeRequired: true,
        cashPaymentAmount: 100.0,
        orderTotal: 75.50,
      );

      const invalidState = DeliveryPaymentState(
        changeRequired: true,
        cashPaymentAmount: 50.0,
        orderTotal: 75.50,
      );

      expect(validState.isValidCashAmount, isTrue);
      expect(invalidState.isValidCashAmount, isFalse);
    });

    test('should be valid when change not required', () {
      const state = DeliveryPaymentState(
        changeRequired: false,
        cashPaymentAmount: 0.0,
        orderTotal: 75.50,
      );

      expect(state.isValidCashAmount, isTrue);
    });

    test('should handle edge cases', () {
      // Zero amounts
      const zeroState = DeliveryPaymentState(
        changeRequired: true,
        cashPaymentAmount: 0.0,
        orderTotal: 0.0,
      );
      expect(zeroState.calculatedChange, equals(0.0));

      // Large amounts
      const largeState = DeliveryPaymentState(
        changeRequired: true,
        cashPaymentAmount: 1000.0,
        orderTotal: 999.99,
      );
      expect(largeState.calculatedChange, closeTo(0.01, 0.001));
    });
  });
}
