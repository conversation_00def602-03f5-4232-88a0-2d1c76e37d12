import 'package:pandoo_delivery/infrastructure/models/data/currency_data.dart';
import 'package:pandoo_delivery/infrastructure/models/response/languages_response.dart';
import 'package:pandoo_delivery/infrastructure/services/local_storage.dart';
import 'package:flutter/foundation.dart';

/// Service to automatically configure Brazilian defaults for language and currency
/// This eliminates the need for user selection since the app is Brazil-specific
class BrazilianDefaultsService {
  
  /// Default Brazilian Portuguese language configuration
  static final LanguageData _defaultLanguage = LanguageData(
    id: 1,
    title: 'Português',
    locale: 'pt',
    backward: false,
    isDefault: true,
  );
  
  /// Default Brazilian Real currency configuration
  static final CurrencyData _defaultCurrency = CurrencyData(
    id: 1,
    title: 'Real Brasileiro',
    symbol: 'R\$',
    position: 'before',
    active: true,
    isDefault: true,
  );
  
  /// Initialize Brazilian defaults for new users
  static Future<void> initializeBrazilianDefaults() async {
    try {
      // Set default language if not already set
      if (LocalStorage.getLanguage() == null) {
        await LocalStorage.setLanguageData(_defaultLanguage);
        await LocalStorage.setLangLtr(false); // Portuguese is LTR (backward = false)
        await LocalStorage.setLanguageSelected(true);
        
        if (kDebugMode) {
          debugPrint('✅ Brazilian Defaults: Portuguese language set');
        }
      }
      
      // Set default currency if not already set
      if (LocalStorage.getSelectedCurrency() == null) {
        await LocalStorage.setSelectedCurrency(_defaultCurrency);
        
        if (kDebugMode) {
          debugPrint('✅ Brazilian Defaults: Brazilian Real currency set');
        }
      }
      
      if (kDebugMode) {
        debugPrint('✅ Brazilian Defaults: Initialization completed');
      }
      
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Brazilian Defaults: Error during initialization: $e');
      }
    }
  }
  
  /// Force set Brazilian defaults (useful for testing or reset)
  static Future<void> forceBrazilianDefaults() async {
    try {
      await LocalStorage.setLanguageData(_defaultLanguage);
      await LocalStorage.setLangLtr(true);
      await LocalStorage.setLanguageSelected(true);
      await LocalStorage.setSelectedCurrency(_defaultCurrency);
      
      if (kDebugMode) {
        debugPrint('✅ Brazilian Defaults: Force set completed');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Brazilian Defaults: Error during force set: $e');
      }
    }
  }
  
  /// Check if Brazilian defaults are properly configured
  static bool areBrazilianDefaultsSet() {
    final language = LocalStorage.getLanguage();
    final currency = LocalStorage.getSelectedCurrency();
    
    final languageSet = language != null && language.locale == 'pt';
    final currencySet = currency != null && currency.symbol == 'R\$';
    
    return languageSet && currencySet;
  }
  
  /// Get the default Brazilian language
  static LanguageData get defaultLanguage => _defaultLanguage;
  
  /// Get the default Brazilian currency
  static CurrencyData get defaultCurrency => _defaultCurrency;
  
  /// Check if current language is Portuguese
  static bool get isPortugueseSet {
    final language = LocalStorage.getLanguage();
    return language?.locale == 'pt';
  }
  
  /// Check if current currency is Brazilian Real
  static bool get isBrazilianRealSet {
    final currency = LocalStorage.getSelectedCurrency();
    return currency?.symbol == 'R\$';
  }
  
  /// Get status message for debugging
  static String getStatusMessage() {
    final language = LocalStorage.getLanguage();
    final currency = LocalStorage.getSelectedCurrency();
    
    return '''
Brazilian Defaults Status:
- Language: ${language?.title ?? 'Not set'} (${language?.locale ?? 'N/A'})
- Currency: ${currency?.title ?? 'Not set'} (${currency?.symbol ?? 'N/A'})
- Defaults Set: ${areBrazilianDefaultsSet() ? 'Yes' : 'No'}
''';
  }
}
