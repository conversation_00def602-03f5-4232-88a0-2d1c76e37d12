import 'package:intl/intl.dart';
import 'package:pandoo_delivery/infrastructure/services/local_storage.dart';

/// Brazilian Portuguese date localization service
/// Provides proper pt-BR formatting for dates, times, and relative dates
class BrazilianDateService {
  static const String _ptBRLocale = 'pt_BR';
  
  /// Get current locale, defaulting to pt-BR for Portuguese users
  static String get _locale {
    final currentLocale = LocalStorage.getLanguage()?.locale;
    return currentLocale == 'pt' || currentLocale == 'pt-BR' ? _ptBRLocale : (currentLocale ?? 'en');
  }
  
  /// Brazilian weekday names (full)
  static const Map<int, String> _weekdayNames = {
    1: 'Segunda-feira',
    2: 'Terça-feira', 
    3: 'Quarta-feira',
    4: 'Quinta-feira',
    5: '<PERSON><PERSON>-feira',
    6: 'Sábado',
    7: 'Domingo',
  };
  
  /// Brazilian weekday names (short)
  static const Map<int, String> _weekdayNamesShort = {
    1: 'Seg',
    2: 'Ter',
    3: 'Qua', 
    4: 'Qui',
    5: 'Sex',
    6: 'Sáb',
    7: 'Dom',
  };
  
  /// Brazilian month names (full)
  static const Map<int, String> _monthNames = {
    1: 'Janeiro',
    2: 'Fevereiro',
    3: 'Março',
    4: 'Abril',
    5: 'Maio',
    6: 'Junho',
    7: 'Julho',
    8: 'Agosto',
    9: 'Setembro',
    10: 'Outubro',
    11: 'Novembro',
    12: 'Dezembro',
  };
  
  /// Brazilian month names (short)
  static const Map<int, String> _monthNamesShort = {
    1: 'Jan',
    2: 'Fev',
    3: 'Mar',
    4: 'Abr',
    5: 'Mai',
    6: 'Jun',
    7: 'Jul',
    8: 'Ago',
    9: 'Set',
    10: 'Out',
    11: 'Nov',
    12: 'Dez',
  };
  
  /// Format date in Brazilian format (DD/MM/YYYY)
  static String formatBrazilianDate(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy', _locale).format(date);
  }
  
  /// Format date and time in Brazilian format (DD/MM/YYYY HH:mm)
  static String formatBrazilianDateTime(DateTime? date) {
    if (date == null) return '';
    return DateFormat('dd/MM/yyyy HH:mm', _locale).format(date);
  }
  
  /// Format time in 24-hour format (HH:mm)
  static String formatBrazilianTime(DateTime? date) {
    if (date == null) return '';
    return DateFormat('HH:mm', _locale).format(date);
  }
  
  /// Get weekday name in Portuguese
  static String getWeekdayName(DateTime? date, {bool short = false}) {
    if (date == null) return '';
    
    if (_locale == _ptBRLocale) {
      final map = short ? _weekdayNamesShort : _weekdayNames;
      return map[date.weekday] ?? '';
    }
    
    return DateFormat(short ? 'E' : 'EEEE', _locale).format(date);
  }
  
  /// Get month name in Portuguese
  static String getMonthName(DateTime? date, {bool short = false}) {
    if (date == null) return '';
    
    if (_locale == _ptBRLocale) {
      final map = short ? _monthNamesShort : _monthNames;
      return map[date.month] ?? '';
    }
    
    return DateFormat(short ? 'MMM' : 'MMMM', _locale).format(date);
  }
  
  /// Format relative date (hoje, ontem, amanhã)
  static String formatRelativeDate(DateTime? date) {
    if (date == null) return '';
    
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    final difference = targetDate.difference(today).inDays;
    
    if (_locale == _ptBRLocale) {
      switch (difference) {
        case -1:
          return 'Ontem';
        case 0:
          return 'Hoje';
        case 1:
          return 'Amanhã';
        default:
          if (difference > 1 && difference <= 7) {
            return getWeekdayName(date);
          }
          return formatBrazilianDate(date);
      }
    }
    
    // Fallback to English
    switch (difference) {
      case -1:
        return 'Yesterday';
      case 0:
        return 'Today';
      case 1:
        return 'Tomorrow';
      default:
        if (difference > 1 && difference <= 7) {
          return getWeekdayName(date);
        }
        return formatBrazilianDate(date);
    }
  }
  
  /// Format delivery date with relative context
  static String formatDeliveryDate(DateTime? date) {
    if (date == null) return '';
    
    final relativeDate = formatRelativeDate(date);
    if (relativeDate == 'Hoje' || relativeDate == 'Today') {
      return relativeDate;
    }
    
    if (_locale == _ptBRLocale) {
      return '$relativeDate, ${date.day} de ${getMonthName(date, short: true)}';
    }
    
    return DateFormat('EEEE, MMM dd', _locale).format(date);
  }
  
  /// Format order history date
  static String formatOrderDate(DateTime? date) {
    if (date == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return _locale == _ptBRLocale ? 'Hoje' : 'Today';
    } else if (difference == 1) {
      return _locale == _ptBRLocale ? 'Ontem' : 'Yesterday';
    } else if (difference < 7) {
      return getWeekdayName(date);
    }
    
    return formatBrazilianDate(date);
  }
  
  /// Format relative time for notifications (e.g., "há 2 horas", "há 3 dias")
  static String formatRelativeTime(DateTime? date) {
    if (date == null) return '';

    final now = DateTime.now();
    final difference = now.difference(date);

    // Handle future dates (should not happen for notifications, but just in case)
    if (difference.isNegative) {
      return _locale == _ptBRLocale ? 'agora mesmo' : 'just now';
    }

    if (_locale == _ptBRLocale) {
      // Portuguese formatting
      if (difference.inSeconds < 60) {
        return 'agora mesmo';
      } else if (difference.inMinutes < 60) {
        final minutes = difference.inMinutes;
        return minutes == 1 ? 'há 1 minuto' : 'há $minutes minutos';
      } else if (difference.inHours < 24) {
        final hours = difference.inHours;
        return hours == 1 ? 'há 1 hora' : 'há $hours horas';
      } else if (difference.inDays < 7) {
        final days = difference.inDays;
        return days == 1 ? 'há 1 dia' : 'há $days dias';
      } else if (difference.inDays < 30) {
        final weeks = (difference.inDays / 7).floor();
        return weeks == 1 ? 'há 1 semana' : 'há $weeks semanas';
      } else if (difference.inDays < 365) {
        final months = (difference.inDays / 30).floor();
        return months == 1 ? 'há 1 mês' : 'há $months meses';
      } else {
        final years = (difference.inDays / 365).floor();
        return years == 1 ? 'há 1 ano' : 'há $years anos';
      }
    } else {
      // English formatting (fallback)
      if (difference.inSeconds < 60) {
        return 'just now';
      } else if (difference.inMinutes < 60) {
        final minutes = difference.inMinutes;
        return minutes == 1 ? '1 minute ago' : '$minutes minutes ago';
      } else if (difference.inHours < 24) {
        final hours = difference.inHours;
        return hours == 1 ? '1 hour ago' : '$hours hours ago';
      } else if (difference.inDays < 7) {
        final days = difference.inDays;
        return days == 1 ? '1 day ago' : '$days days ago';
      } else if (difference.inDays < 30) {
        final weeks = (difference.inDays / 7).floor();
        return weeks == 1 ? '1 week ago' : '$weeks weeks ago';
      } else if (difference.inDays < 365) {
        final months = (difference.inDays / 30).floor();
        return months == 1 ? '1 month ago' : '$months months ago';
      } else {
        final years = (difference.inDays / 365).floor();
        return years == 1 ? '1 year ago' : '$years years ago';
      }
    }
  }

  /// Check if current locale is Portuguese
  static bool get isPortuguese => _locale == _ptBRLocale;

  /// Get localized "de" preposition for dates
  static String get datePreposition => isPortuguese ? 'de' : '';
}
