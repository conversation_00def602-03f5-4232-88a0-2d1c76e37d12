import 'package:flutter/foundation.dart';
import 'package:pandoo_delivery/infrastructure/services/payment_fallback_service.dart';

class PaymentTestService {
  /// Test all payment methods to ensure they work correctly
  static void runPaymentTests() {
    if (!kDebugMode) return;

    debugPrint('\n🧪 ========== PAYMENT TESTS ==========');

    // Test delivery payment methods
    _testDeliveryPayments();

    // Test gateway payment methods
    _testGatewayPayments();

    // Test fallback functionality
    _testFallbackFunctionality();

    debugPrint('🧪 ====================================\n');
  }
  
  static void _testDeliveryPayments() {
    debugPrint('\n📱 Testing Delivery Payment Methods:');

    final deliveryMethods = ['cash', 'pix', 'card', 'wallet'];

    for (final method in deliveryMethods) {
      final shouldSkip = PaymentFallbackService.shouldSkipProcessing(method);
      final displayName = PaymentFallbackService.getPaymentDisplayName(method);

      debugPrint('  $method: ${shouldSkip ? '✅ SKIP PROCESSING' : '❌ WILL PROCESS'} - $displayName');

      if (!shouldSkip) {
        debugPrint('    ⚠️  WARNING: $method should skip processing but doesn\'t!');
      }
    }
  }
  
  static void _testGatewayPayments() {
    debugPrint('\n💳 Testing Gateway Payment Methods:');

    final gatewayMethods = ['stripe', 'paypal', 'mercado-pago', 'pay-fast'];

    for (final method in gatewayMethods) {
      final shouldSkip = PaymentFallbackService.shouldSkipProcessing(method);
      final displayName = PaymentFallbackService.getPaymentDisplayName(method);

      debugPrint('  $method: ${shouldSkip ? '❌ WILL SKIP' : '✅ WILL PROCESS'} - $displayName');

      if (shouldSkip) {
        debugPrint('    ⚠️  WARNING: $method should process but will skip!');
      }
    }
  }
  
  static void _testFallbackFunctionality() {
    debugPrint('\n🔄 Testing Fallback Functionality:');

    final testCases = {
      'cash_delivery': 'cash',
      'card_delivery': 'card',
      'pix_delivery': 'pix',
      'dinheiro': 'cash',
      'cartao': 'card',
      'stripe': 'stripe', // Should remain the same
    };

    testCases.forEach((original, expected) {
      final corrected = PaymentFallbackService.getCorrectPaymentTag(original);
      final isCorrect = corrected == expected;

      debugPrint('  $original → $corrected ${isCorrect ? '✅' : '❌'}');

      if (!isCorrect) {
        debugPrint('    Expected: $expected, Got: $corrected');
      }
    });
  }
  
  /// Test specific payment flow
  static void testPaymentFlow(String paymentTag) {
    if (!kDebugMode) return;

    debugPrint('\n🔍 Testing Payment Flow for: $paymentTag');

    final correctedTag = PaymentFallbackService.getCorrectPaymentTag(paymentTag);
    final shouldSkip = PaymentFallbackService.shouldSkipProcessing(correctedTag);
    final isValid = PaymentFallbackService.isValidPaymentTag(correctedTag);
    final displayName = PaymentFallbackService.getPaymentDisplayName(correctedTag);

    debugPrint('  Original Tag: $paymentTag');
    debugPrint('  Corrected Tag: $correctedTag');
    debugPrint('  Should Skip Processing: $shouldSkip');
    debugPrint('  Is Valid: $isValid');
    debugPrint('  Display Name: $displayName');

    if (shouldSkip) {
      debugPrint('  ✅ Will create order directly (no gateway processing)');
    } else {
      debugPrint('  🌐 Will process through payment gateway');
      final endpoints = PaymentFallbackService.getFallbackEndpoints(correctedTag);
      debugPrint('  Endpoints to try:');
      for (int i = 0; i < endpoints.length; i++) {
        debugPrint('    ${i + 1}. ${endpoints[i]}');
      }
    }

    debugPrint('');
  }
  
  /// Simulate order creation with payment data
  static Map<String, dynamic> simulateOrderCreation(String paymentTag) {
    final correctedTag = PaymentFallbackService.getCorrectPaymentTag(paymentTag);
    final shouldSkip = PaymentFallbackService.shouldSkipProcessing(correctedTag);
    
    final orderData = {
      'cart_id': 24,
      'shop_id': 501,
      'payment_id': 1,
      'currency_id': 1,
      'rate': 1,
      'delivery_type': 'delivery',
      'type': 'mobile',
      'payment_method': correctedTag,
    };
    
    // Add delivery payment specific fields
    if (shouldSkip) {
      if (correctedTag == 'cash') {
        orderData['change_required'] = false;
        // Don't add change_amount if null
      }
      orderData['payment_notes'] = 'Pagamento na entrega';
    }
    
    if (kDebugMode) {
      debugPrint('📦 Simulated Order Data:');
      orderData.forEach((key, value) {
        debugPrint('  $key: $value');
      });
    }
    
    return orderData;
  }
}
