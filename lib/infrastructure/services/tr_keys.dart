class TrKeys {
  TrKeys._();

  static const String bgPicture = 'bg_picture';
  static const String documents = 'documents';
  static const String uploadDocuments = 'upload.documents';
  static const String helpInfo = 'help.info';
  static const String uiType = 'ui_type';
  static const String orderImage = 'order.image';
  static const String especiallyForYou = 'especially_for_you';
  static const String workForYou = 'work_for_you';
  static const String readAll = 'read_all';
  static const String to = 'to';
  static const String created = 'created';
  static const String offers = 'offers';
  static const String notValidDate = 'not.valid.date';
  static const String searchTheMenu = 'search.the.menu';
  static const String tellThisCodeToDriver = 'tell.this.code.to.driver';
  static const String thisImageWasUploadDriver =
      'this.image.was.uploaded.by.driver';
  static const String price = 'price';
  static const String customTip = 'custom.tip';
  static const String tips = 'would.you.like.to.add.a.tip?';
  static const String deliveryTip = 'delivery.tip';
  static const String parcels = 'parcels';
  static const String home = 'home';
  static const String privacy = 'privacy';
  static const String reservation = 'reservation';
  static const String best = 'best';
  static const String game = 'game';
  static const String custom = 'custom';
  static const String wantToPlayGame = 'want.to.play.game';
  static const String youWin = 'you.win';
  static const String newGame = 'new.game';
  static const String gameOver = 'game.over';
  static const String score = 'score';
  static const String tryAgain = 'try.again';
  static const String thisFieldIsRequired = 'this.field.is.required';
  static const String ifYouWantToUseThisService =
      'if.you.want.to.use.this.service';
  static const String receiver = 'receiver';
  static const String selectPaymentMethod = 'select.payment.method';
  static const String youWritePhoneAndFirstname =
      'you.need.write.phone.and.firstname.for.someone';
  static const String sm = 'sm';
  static const String yourOrderDidNotReachMinAmountMinAmountIs =
      'your.order.did.not.reach.min.amount.min.amount.is';
  static const String unpaid = 'unpaid';
  static const String payLater = 'pay.later';
  static const String pay = 'pay';
  static const String iWantToOrderForSomeone = 'I.want.to.order.for.someone';
  static const String expensive = 'expensive';
  static const String leastExpensive = 'least_expensive';
  static const String standard = 'standard';
  static const String gotIt = 'got_it';
  static const String addAddress = 'add_address';
  static const String selectAddress = 'select_address';
  static const String type = 'type';
  static const String parcelDetail = 'parcel_details';
  static const String activeParcel = 'active_parcel';
  static const String parcelHistory = 'parcel_history';
  static const String itemValue = 'item_value';
  static const String remainAnonymous = 'remain_anonymous';
  static const String dontNotifyRecipient = 'dont_notify_a_recipient';
  static const String recipient = 'recipient';
  static const String sender = 'sender';
  static const String explore = 'explore';
  static const String addInstruction = 'add_instruction';
  static const String newItem = 'new.items.with.a.discount';
  static const String whatAreYouSending = 'what_are_you_sending';
  static const String itemDescription = 'item_description';
  static const String chooseBrand = 'choose_brand';
  static const String needSelectProduct = 'need_select_product';
  static const String imageGenerateTake = 'image_generate_take';
  static const String generateImageWithChatGPT = 'generate_image_with_chatGPT';
  static const String gallery = 'gallery';
  static const String set = 'set_up_delivery';
  static const String fast = 'fast_secure_delivery';
  static const String deliveryRestriction = 'delivery_restriction';
  static const String saveTime = 'save_time';
  static const String back = 'back';
  static const String kg = 'kg';
  static const String upTo = 'up.to';
  static const String howItWorks = 'how_it_works';
  static const String errorWithConnectingToFirebase =
      'error_with_connecting_to_firebase';
  static const String doYouLeaveGroup = 'do_you_leave_group';
  static const String chatWithAdmin = 'chat_with_admin';
  static const String select = 'select';
  static const String deliveryTo = 'delivery.to';
  static const String cart = 'cart';
  static const String learnMore = 'learn.more';
  static const String yourPersonalDoor = 'your.personal.door';
  static const String doorToDoor = 'door_to_door';
  static const String stories = 'stories';
  static const String favouriteBrand = 'favourite.brands';
  static const String popularNearYou = 'popular.near.you';
  static const String fosend = 'fosend';
  static const String recently = 'recently';
  static const String thankYouForOrder = 'thank_you_for_order';
  static const String typeSomething = 'type_something';
  static const String groupOrder = 'group_order';
  static const String allServices = 'all_services';
  static const String isEditOrder = 'is_edit_order';
  static const String leaveGroup = 'leave_group';
  static const String joinOrder = 'join_order';
  static const String join = 'join';
  static const String youCanOnly = 'you_can_only';
  static const String branches = 'branches';
  static const String notWork = "not_work";
  static const String emailOrPhoneNumber = "email_or_phone_number";
  static const String timeSchedule = "time_schedule";
  static const String moreInfo = 'more_info';
  static const String canNotBeEmpty = 'can_not_be_empty';
  static const String coped = 'copied';
  static const String referralFaq = 'referral_faq';
  static const String ratings = 'ratings';
  static const String yourOrderStatusChanged =
      'your_order_status_has_been_changed';
  static const String openShop = 'open_shop';
  static const String deals = 'deals';
  static const String maxQty = 'max_qty';
  static const String openUntil = 'open_until';
  static const String notEnoughMoney = 'not_enough_money';
  static const String signUpToDeliver = 'sign_up_to_deliver';
  static const String close = 'close';
  static const String terms = 'terms';
  static const String privacyPolicy = 'privacy_policy';
  static const String about = 'about';
  static const String careers = 'careers';
  static const String balance = 'balance';
  static const String share = 'share';
  static const String copyCode = 'copy_code';
  static const String inviteFriend = 'invite_friend';
  static const String referralIncorrect = 'referral_incorrect';
  static const String referral = 'referral';
  static const String goToAdminPanel = 'go_to_admin_panel';
  static const String emailAlreadyExists = 'email_already_exists';
  static const String yourRequest = 'your_request_is_being_processed';
  static const String deliveryTimeFrom = 'delivery_time_from';
  static const String deliveryTimeTo = 'delivery_time_to';
  static const String startPrice = 'start_price';
  static const String pricePerKm = 'price_per_km';
  static const String deliveryTimeType = 'delivery_time_type';
  static const String recommendedSize = "recommended_size";
  static const String productPicture = 'product_picture';
  static const String restaurant = 'restaurant';
  static const String description = 'description';
  static const String restaurantName = 'restaurant_name';
  static const String noDriverZone = "no_driver_zone";
  static const String minQty = 'min_qty';
  static const String deposit = 'deposit_withdrawl';
  static const String paymentDate = 'payment_date';
  static const String transactions = 'transactions';
  static const String done = 'done';
  static const String owner = 'owner';
  static const String started = 'started';
  static const String groupOrderProgress = 'group_order_progress';
  static const String deleteUser = 'delete_user';
  static const String ingredients = 'ingredients';
  static const String choosing = 'choosing';
  static const String youFullyManaga = 'you_fully_manage';
  static const String addAddressInformation = 'add_address_information';
  static const String wallet = 'wallet';
  static const String repeatOrder = 'repeat_order';
  static const String autoOrder = 'auto_order';
  static const String removeAutoOrder = 'remove_auto_order';
  static const String autoOrderInfo = 'auto_order_info';
  static const String autoOrderCreatedSuccessfully =
      'auto_order_created_successfully';
  static const String autoOrderDeletedSuccessfully =
      'auto_order_deleted_successfully';
  static const String paymentMethodFailed = 'payment_method_failed';
  static const String typeHere = 'type_here';
  static const String notification = 'notification';
  static const String lowRating = 'low_rating';
  static const String lowSale = 'low_sale';
  static const String highlyRated = 'highly_rated';
  static const String bestSale = 'best_sale';
  static const String trustYou = 'trust_you';
  static const String deleteAccount = 'delete_account';
  static const String areYouSure = 'are_you_sure';
  static const String answer = 'answer';
  static const String cause = 'cause';
  static const String successfully = 'successfully';
  static const String whyDoYouWant = 'why_do_you_want';
  static const String wantIt = 'want_it';
  static const String count = 'count';
  static const String giftBuy = 'gift_buy';
  static const String paymentTypeIsNotAdded = 'payment_type_is_not_added';
  static const String youCantCreateOrder = 'you_cant_create_order';
  static const String noPaymentType = 'no_payment_type';
  static const String loading = 'loading';
  static const String reFound = 'refound';
  static const String thisTimeIsNotAvailable = "this_time_is_not_available";
  static const String freeDelivery = 'free_delivery';
  static const String sortBy = 'sort_by';
  static const String specialOffers = 'special_offers';
  static const String rating = 'rating';
  static const String priceRange = 'price_range';
  static const String from = 'from';
  static const String shopAndRestaurants = 'shop_and_restaurants';
  static const String show = "show";
  static const String under = "under";
  static const String noDriver = "no_driver";
  static const String driver = "driver";
  static const String id = "id";
  static const String youHavePromoCode = "you_have_promo_code";
  static const String notWorkTodayTime = "not_work_today_time";
  static const String notWorkTomorrow = "not_work_tomorrow";
  static const String notWorkToday = "not_work_today";
  static const String notWorkTodayAndTomorrow = "not_work_today_and_tomorrow";
  static const String bonus = "bonus";
  static const String shopBonus = "shop.bonus";
  static const String totalDiscount = 'total_discount';
  static const String shopTax = 'shop_tax';
  static const String totalTax = 'total_tax';
  static const String subtotal = 'subtotal';
  static const String min = "min";
  static const String other = "other";
  static const String tomorrow = "tomorrow";
  static const String activeOrders = "active_orders";
  static const String cash = "cash";
  static const String today = "today";
  static const String timeDelivery = "time_delivery";
  static const String callToSupport = 'call_to_support';
  static const String stillHaveQuestions = 'still_have_questions';
  static const String cantFindTheAnswer = 'cant_find_the_answer';
  static const String cartIsEmpty = 'cart_is_empty';
  static const String allPreviouslyAdded = "all_previously_added";
  static const String agreeLocation = 'agree_location';
  static const String nothingFound = 'nothing_found';
  static const String trySearchingAgain = "try_searching_again";
  static const String orderNow = 'order_now';
  static const String results = 'results';
  static const String found = 'found';
  static const String restaurants = 'restaurants';
  static const String noRestaurant = 'no_restaurant';
  static const String ratingCourier = 'rating_courier';
  static const String sendMessage = 'send_message';
  static const String callTheDriver = 'call_the_driver';
  static const String callCenterRestaurant = 'call_center_restaurant';
  static const String compositionOrder = 'composition_order';
  static const String promoCode = 'promo_code';
  static const String addPromoCode = 'add_promocode';
  static const String paymentMethods = 'payment_methods';
  static const String continueToPayment = 'continue_to_payment';
  static const String yourOrder = 'your_orders';
  static const String comment = 'comment';
  static const String floor = 'floor';
  static const String house = 'house';
  static const String office = 'office';
  static const String clearCard1 = 'your_card_below';
  static const String clearCard2 = 'do_you_want_to_delete_it?';
  static const String cvc = 'CVC';
  static const String expiredDate = 'expired_date';
  static const String fullName = 'full_name';
  static const String cardNumber = 'card_number';
  static const String addNewCart = 'add_new_card';
  static const String likeRestaurants = 'liked_restaurant';
  static const String add = 'add';
  static const String groupMember = 'group_member';
  static const String manageOrder = 'manage_order';
  static const String start = 'start';
  static const String startGroupOrder = 'start_group_order';
  static const String clear = 'clear';
  static const String clearCard = 'you_really_want_to_clear_the_card';
  static const String serviceFee = 'service_fee';
  static const String deliveryPrice = 'delivery_price';
  static const String total = 'total';
  static const String mobileNumber = 'mobile_number';
  static const String alternativeNumber = 'alternative_number';
  static const String help = 'help';
  static const String setting = 'settings';
  static const String allRestaurants = 'popular.near.you';
  static const String newsOfWeek = 'news_of_the_week';
  static const String popular = 'popular';
  static const String recommended = 'recommended';
  static const String send = 'send';
  static const String resetPasswordText = "reset_password_text";
  static const String resendOtp = 'send_new';
  static const String sendOtp = 'we_are_send_OTP_code_to';
  static const String enterOtp = 'enter_OTP_code';
  static const String county = 'country';
  static const String orAccessQuickly = 'or_access_quickly';
  static const String keepLogged = 'keep_me_logged_in';
  static const String foodyman = 'foodyman';
  static const String shopList = 'shop_list';
  static const String viewMap = 'view_map';
  static const String address = 'address';
  static const String personalInformation = 'personal_information';
  static const String savedStores = 'saved_stores';
  static const String discount = 'discount';
  static const String viewedProducts = 'viewed_products';
  static const String walletHistory = 'wallet_history';
  static const String blogs = 'blogs';
  static const String savedLocations = 'saved_locations';
  static const String orderHistory = 'order_history';
  static const String chat = 'chat';
  static const String becomeSeller = 'become_seller';
  static const String logout = 'logout';
  static const String noWallet = 'no_wallet';
  static const String systemSettings = 'system_settings';
  static const String selectLanguage = 'select_language';
  static const String next = 'next';
  static const String skip = 'skip';
  static const String login = 'login';
  static const String email = 'email';
  static const String password = 'password';
  static const String forgotPassword = 'forgot_password';
  static const String continueWithGoogle = 'continue_with_google';
  static const String dontHaveAnAcc = 'dont_have_an_account';
  static const String register = 'register';
  static const String enterADeliveryAddress = 'enter_a_delivery_address';
  static const String confirmLocation = 'confirm_location';
  static const String title = 'title';
  static const String save = 'save';
  static const String searchProducts = 'search_restaurant_and_products';
  static const String all = 'all';
  static const String openNow = 'open_now';
  static const String newKey = 'new';
  static const String allShops = 'all_shops';
  static const String work247 = 'work_247';
  static const String pickup = 'pickup';
  static const String pickupAt = 'pickup_at';
  static const String language = 'languages';
  static const String currency = 'currency';
  static const String theme = 'theme';
  static const String notifications = 'notifications';
  static const String filter = 'filter';
  static const String clearAll = 'clear_all';
  static const String categories = 'categories';
  static const String brands = 'brands';
  static const String apply = 'apply';
  static const String thereAreNoItemsInThe = 'there_are_no_items_in_the';
  static const String openNowShops = 'open_now_shops';
  static const String newShops = 'new_shops';
  static const String profile = 'profile';
  static const String order = 'orders';
  static const String liked = 'liked';
  static const String products = 'products';
  static const String likedProducts = 'liked_products';
  static const String allStores = 'all_stores';
  static const String goToCheckout = 'go_to_checkout';
  static const String myOrder = 'my_order';
  static const String deleteAll = 'delete_all';
  static const String searchIn = 'in';
  static const String storeInfo = 'store_info';
  static const String storeDeliveryTimes = 'store_delivery_times';
  static const String saved = 'saved';
  static const String seeMore = 'see_more';
  static const String seeAll = 'see_all';
  static const String mostSoldProducts = 'most_sold_products';
  static const String discountProducts = 'discount_products';
  static const String writeComment = 'write_comment';
  static const String topSales = 'top_sales';
  static const String newProducts = 'new_products';
  static const String deliveryTimes = 'delivery_times';
  static const String store = 'store';
  static const String workingHours = 'working_hours';
  static const String deliveryRange = 'delivery_range';
  static const String phone = 'phone';

  static const String search = 'search';
  static const String checkYourNetworkConnection =
      'check_your_network_connection';
  static const String somethingWentWrongWithTheServer =
      'something_went_wrong_with_the_server';
  static const String totalProductPrice = 'total_product_price';
  static const String totalProductTax = 'total_product_tax';
  static const String totalShopTax = 'total_shop_tax';
  static const String deliveryFee = 'delivery_fee';
  static const String coupon = 'coupon';
  static const String totalAmount = 'total_amount';
  static const String shop = 'shop';
  static const String tax = 'total_tax';
  static const String totalPrice = 'total_price';

  static const String cancelOrder = 'cancel_order';
  static const String continueText = 'continue';
  static const String free = 'free';
  static const String productNote = 'product_note';
  static const String delivery = 'delivery';
  static const String deliveryAddress = 'delivery_address';
  static const String deliveryTime = 'delivery_time';
  static const String noSearchResults = 'no_search_results';
  static const String banner = 'banner';
  static const String currencies = 'currencies';
  static const String profileSettings = 'profile_settings';
  static const String generalInfo = 'general_info';
  static const String firstname = 'firstname';
  static const String surname = 'surname';
  static const String gender = 'gender';
  static const String male = 'male';
  static const String female = 'female';
  static const String dateOfBirth = 'date_of_birth';
  static const String phoneNumber = 'phone_number';
  static const String cancel = 'cancel';
  static const String noInternetConnection = 'no_internet_connection';
  static const String resetPassword = 'reset_password';
  static const String confirmation = 'confirm';
  static const String confirmPassword = 'confirm_password';
  static const String shops = 'shops';
  static const String notFound = 'not_found';
  static const String emailNotVerifiedYet = 'email_not_verified_yet';
  static const String yes = 'yes';
  static const String emailIsNotValid = 'email_is_not_valid';

  static const String passwordShouldContainMinimum8Characters =
      'password_should_contain_minimum_8_characters';
  static const String phoneNumberIsNotValid = 'phone_number_is_not_valid';
  static const String confirmationCodeIsNotPresent =
      'confirmation_code_is_not_present';
  static const String confirmPasswordIsNotTheSame =
      'confirm_password_is_not_the_same';
  static const String errorWithUpdatingPassword =
      'error_with_updating_password';

  // Brazilian Payment Methods
  static const String payNow = 'pay_now';
  static const String payNowDescription = 'pay_now_description';
  static const String payOnDelivery = 'pay_on_delivery';
  static const String payOnDeliveryDescription = 'pay_on_delivery_description';
  static const String selectDeliveryPayment = 'select_delivery_payment';
  static const String noDeliveryPaymentMethods = 'no_delivery_payment_methods';
  static const String needChange = 'need_change';
  static const String changeForAmount = 'change_for_amount';
  static const String cashPaymentAmount = 'cash_payment_amount';
  static const String cashPaymentAmountHint = 'cash_payment_amount_hint';
  static const String changeToReturn = 'change_to_return';
  static const String changeSummary = 'change_summary';
  static const String driverWillReturn = 'driver_will_return';
  static const String cashAmountMustBeGreater = 'cash_amount_must_be_greater';
  static const String no = 'no';
  static const String confirm = 'confirm';
  static const String cashDelivery = 'cash_delivery';
  static const String cardDelivery = 'card_delivery';
  static const String pixDelivery = 'pix_delivery';
  static const String debitDelivery = 'debit_delivery';
  static const String noPaymentMethods = 'no_payment_methods';
}
