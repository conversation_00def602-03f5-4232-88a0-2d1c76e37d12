import 'package:pandoo_delivery/infrastructure/models/data/delivery_payment_method.dart';
import 'package:pandoo_delivery/infrastructure/models/response/delivery_payment_methods_response.dart';

class BrazilianPaymentMethods {
  /// Get the default Brazilian delivery payment methods
  static List<DeliveryPaymentMethod> getDefaultMethods() {
    return [
      const DeliveryPaymentMethod(
        tag: 'pix_delivery',
        name: 'PIX na Entrega',
        icon: '📱',
        description: 'Pague via PIX para o restaurante na entrega',
        supportsChange: false,
        maxChangeAmount: 0.0,
      ),
      const DeliveryPaymentMethod(
        tag: 'card_credit_delivery',
        name: 'Cartão de Crédito na Entrega',
        icon: '💳',
        description: 'Pague com cartão de crédito na entrega',
        supportsChange: false,
        maxChangeAmount: 0.0,
      ),
      const DeliveryPaymentMethod(
        tag: 'card_debit_delivery',
        name: 'Cartão de Débito na Entrega',
        icon: '💳',
        description: 'Pague com cartão de débito na entrega',
        supportsChange: false,
        maxChangeAmount: 0.0,
      ),
      const DeliveryPaymentMethod(
        tag: 'cash_delivery',
        name: '<PERSON><PERSON><PERSON> na Entrega',
        icon: '💵',
        description: 'Pague com dinheiro (informe se precisa de troco)',
        supportsChange: true,
        maxChangeAmount: 500.0, // Maximum change amount in BRL
      ),
    ];
  }

  /// Get payment methods response for a specific shop
  static DeliveryPaymentMethodsResponse getPaymentMethodsForShop({
    required int shopId,
    List<String>? enabledMethods,
  }) {
    final allMethods = getDefaultMethods();
    
    // Filter methods based on shop preferences
    final filteredMethods = enabledMethods != null
        ? allMethods.where((method) => enabledMethods.contains(method.tag)).toList()
        : allMethods;

    return DeliveryPaymentMethodsResponse(
      deliveryPaymentMethods: filteredMethods,
      instructions: _getInstructionsForShop(shopId),
      shopId: shopId,
    );
  }

  /// Get payment method display name for UI
  static String getPaymentMethodDisplayName(String? tag) {
    switch (tag) {
      case 'card':
        return 'Cartão na Entrega (Crédito/Débito)';
      case 'pix':
        return 'PIX na Entrega';
      case 'cash':
        return 'Dinheiro na Entrega';
      default:
        return 'Pagar na Entrega';
    }
  }

  /// Get payment method color for UI
  static String getPaymentMethodColor(String? tag) {
    switch (tag) {
      case 'card':
        return 'blue';
      case 'pix':
        return 'green';
      case 'cash':
        return 'orange';
      default:
        return 'default';
    }
  }

  /// Get payment method icon
  static String getPaymentMethodIcon(String? tag) {
    switch (tag) {
      case 'card':
        return '💳';
      case 'pix':
        return '📱';
      case 'cash':
        return '💵';
      default:
        return '💰';
    }
  }

  /// Calculate change amount
  static Map<String, dynamic> calculateChangeAmount(double orderTotal, double cashAmount) {
    final changeAmount = cashAmount - orderTotal;
    
    return {
      'order_total': orderTotal,
      'cash_amount': cashAmount,
      'change_amount': changeAmount > 0 ? changeAmount : 0.0,
      'change_needed': changeAmount > 0,
    };
  }

  /// Validate payment method selection
  static bool isValidPaymentSelection({
    required String? paymentMethod,
    required double orderTotal,
    double? changeAmount,
  }) {
    if (paymentMethod == null || paymentMethod.isEmpty) return false;
    
    if (paymentMethod == 'cash' && changeAmount != null) {
      // For cash payments, ensure change amount is reasonable
      return changeAmount >= orderTotal && changeAmount <= (orderTotal + 500);
    }
    
    return true;
  }

  /// Get instructions for specific shop
  static String _getInstructionsForShop(int shopId) {
    // This could be customized per shop in the future
    return '''
Escolha como você gostaria de pagar na entrega:

• Cartão: O entregador levará a máquina de cartão
• PIX: Você fará o PIX diretamente para o restaurante
• Dinheiro: Informe se precisará de troco

O pagamento será processado apenas no momento da entrega.
    '''.trim();
  }

  /// Format currency for Brazilian Real
  static String formatCurrency(double amount) {
    return 'R\$ ${amount.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Parse currency string to double
  static double parseCurrency(String value) {
    final cleanValue = value
        .replaceAll('R\$', '')
        .replaceAll(' ', '')
        .replaceAll(',', '.')
        .trim();
    
    return double.tryParse(cleanValue) ?? 0.0;
  }
}
