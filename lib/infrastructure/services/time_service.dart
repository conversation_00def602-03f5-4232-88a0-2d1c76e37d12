import 'package:pandoo_delivery/app_constants.dart';
import 'package:intl/intl.dart';
import 'package:pandoo_delivery/infrastructure/services/local_storage.dart';

abstract class TimeService {
  TimeService._();

  static final String _timeFormat = AppConstants.use24Format ? 'HH:mm' : 'h:mm a';

  /// Get current locale for date formatting, defaults to pt-BR for Brazilian users
  static String get _locale {
    final currentLocale = LocalStorage.getLanguage()?.locale;
    // Default to pt-BR for Brazilian Portuguese, fallback to current locale or 'en'
    return currentLocale == 'pt' || currentLocale == 'pt-BR' ? 'pt_BR' : (currentLocale ?? 'en');
  }

  static String dateFormatYMD(DateTime? time) {
    return DateFormat("yyyy-MM-dd").format(time ?? DateTime.now());
  }

  static String dateFormatMDYHm(DateTime? time) {
    return DateFormat("dd/MM/yyyy - $_timeFormat", _locale)
        .format(time ?? DateTime.now());
  }

  static String dateFormatDMY(DateTime? time) {
    return DateFormat("dd/MM/yyyy", _locale).format(time ?? DateTime.now());
  }

  static String dateFormatMD(DateTime? time) {
    return DateFormat("dd 'de' MMMM", _locale).format(time ?? DateTime.now());
  }

  static String dateFormatEMD(DateTime? time) {
    return DateFormat("EEEE, dd 'de' MMM", _locale).format(time ?? DateTime.now());
  }

  static String dateFormatMDHm(DateTime? time) {
    return DateFormat("dd 'de' MMM, $_timeFormat", _locale).format(time ?? DateTime.now());
  }

  static String dateFormatEE(DateTime? time) {
    return DateFormat("EEEE", _locale).format(time ?? DateTime.now());
  }

  static String dateFormatDM(DateTime? time) {
    if (DateTime.now().year == time?.year) {
      return DateFormat("dd 'de' MMMM", _locale).format(time ?? DateTime.now());
    }
    return DateFormat("dd 'de' MMMM 'de' yyyy", _locale).format(time ?? DateTime.now());
  }

  static String timeFormat(DateTime? time) {
    return DateFormat(_timeFormat, _locale).format(time ?? DateTime.now());
  }

  static String dateFormatForChat(DateTime? time) {
    if ((DateTime.now().difference(time ?? DateTime.now()).inHours) > 24 &&
        (DateTime.now().difference(time ?? DateTime.now()).inDays) < 7) {
      return DateFormat("EEEE", _locale).format(time ?? DateTime.now());
    }
    if ((DateTime.now().difference(time ?? DateTime.now()).inDays) > 7) {
      return DateFormat("dd/MM/yyyy", _locale).format(time ?? DateTime.now());
    }
    return DateFormat(_timeFormat, _locale).format(time ?? DateTime.now());
  }

  static String dateFormatForNotification(DateTime? time) {
    return DateFormat("dd 'de' MMM, $_timeFormat", _locale).format(time ?? DateTime.now());
  }

  static String formatHHMMSS(int seconds) {
    seconds = (seconds % 3600).truncate();
    int minutes = (seconds / 60).truncate();
    String minutesStr = (minutes).toString().padLeft(2, '0');
    String secondsStr = (seconds % 60).toString().padLeft(2, '0');
    return "$minutesStr:$secondsStr";
  }
  static String timeFormatTime(String? time) {
    if (time == null) {
      return '';
    }
    return DateFormat(_timeFormat, _locale)
        .format(DateTime.now().copyWith(
      hour: int.tryParse(time.substring(0, 2)),
      minute: int.tryParse(time.substring(3, 5)),
    ));
  }
}
