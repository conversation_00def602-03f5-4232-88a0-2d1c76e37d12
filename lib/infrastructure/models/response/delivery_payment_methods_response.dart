import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pandoo_delivery/infrastructure/models/data/delivery_payment_method.dart';

part 'delivery_payment_methods_response.freezed.dart';

@Freezed(toJson: false, fromJson: false)
class DeliveryPaymentMethodsResponse with _$DeliveryPaymentMethodsResponse {
  const factory DeliveryPaymentMethodsResponse({
    @Default([]) List<DeliveryPaymentMethod> deliveryPaymentMethods,
    @Default('') String instructions,
    int? shopId,
  }) = _DeliveryPaymentMethodsResponse;

  factory DeliveryPaymentMethodsResponse.fromJson(Map<String, dynamic> json) =>
      DeliveryPaymentMethodsResponse(
        deliveryPaymentMethods: (json['deliveryPaymentMethods'] as List<dynamic>?)
            ?.map((e) => DeliveryPaymentMethod.fromJson(e as Map<String, dynamic>))
            .toList() ?? [],
        instructions: json['instructions'] as String? ?? '',
        shopId: json['shopId'] as int?,
      );

  const DeliveryPaymentMethodsResponse._();
}
