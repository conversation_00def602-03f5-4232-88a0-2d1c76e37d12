// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'delivery_payment_methods_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeliveryPaymentMethodsResponse {
  List<DeliveryPaymentMethod> get deliveryPaymentMethods =>
      throw _privateConstructorUsedError;
  String get instructions => throw _privateConstructorUsedError;
  int? get shopId => throw _privateConstructorUsedError;

  /// Create a copy of DeliveryPaymentMethodsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeliveryPaymentMethodsResponseCopyWith<DeliveryPaymentMethodsResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeliveryPaymentMethodsResponseCopyWith<$Res> {
  factory $DeliveryPaymentMethodsResponseCopyWith(
          DeliveryPaymentMethodsResponse value,
          $Res Function(DeliveryPaymentMethodsResponse) then) =
      _$DeliveryPaymentMethodsResponseCopyWithImpl<$Res,
          DeliveryPaymentMethodsResponse>;
  @useResult
  $Res call(
      {List<DeliveryPaymentMethod> deliveryPaymentMethods,
      String instructions,
      int? shopId});
}

/// @nodoc
class _$DeliveryPaymentMethodsResponseCopyWithImpl<$Res,
        $Val extends DeliveryPaymentMethodsResponse>
    implements $DeliveryPaymentMethodsResponseCopyWith<$Res> {
  _$DeliveryPaymentMethodsResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeliveryPaymentMethodsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deliveryPaymentMethods = null,
    Object? instructions = null,
    Object? shopId = freezed,
  }) {
    return _then(_value.copyWith(
      deliveryPaymentMethods: null == deliveryPaymentMethods
          ? _value.deliveryPaymentMethods
          : deliveryPaymentMethods // ignore: cast_nullable_to_non_nullable
              as List<DeliveryPaymentMethod>,
      instructions: null == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String,
      shopId: freezed == shopId
          ? _value.shopId
          : shopId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeliveryPaymentMethodsResponseImplCopyWith<$Res>
    implements $DeliveryPaymentMethodsResponseCopyWith<$Res> {
  factory _$$DeliveryPaymentMethodsResponseImplCopyWith(
          _$DeliveryPaymentMethodsResponseImpl value,
          $Res Function(_$DeliveryPaymentMethodsResponseImpl) then) =
      __$$DeliveryPaymentMethodsResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<DeliveryPaymentMethod> deliveryPaymentMethods,
      String instructions,
      int? shopId});
}

/// @nodoc
class __$$DeliveryPaymentMethodsResponseImplCopyWithImpl<$Res>
    extends _$DeliveryPaymentMethodsResponseCopyWithImpl<$Res,
        _$DeliveryPaymentMethodsResponseImpl>
    implements _$$DeliveryPaymentMethodsResponseImplCopyWith<$Res> {
  __$$DeliveryPaymentMethodsResponseImplCopyWithImpl(
      _$DeliveryPaymentMethodsResponseImpl _value,
      $Res Function(_$DeliveryPaymentMethodsResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeliveryPaymentMethodsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deliveryPaymentMethods = null,
    Object? instructions = null,
    Object? shopId = freezed,
  }) {
    return _then(_$DeliveryPaymentMethodsResponseImpl(
      deliveryPaymentMethods: null == deliveryPaymentMethods
          ? _value._deliveryPaymentMethods
          : deliveryPaymentMethods // ignore: cast_nullable_to_non_nullable
              as List<DeliveryPaymentMethod>,
      instructions: null == instructions
          ? _value.instructions
          : instructions // ignore: cast_nullable_to_non_nullable
              as String,
      shopId: freezed == shopId
          ? _value.shopId
          : shopId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _$DeliveryPaymentMethodsResponseImpl
    extends _DeliveryPaymentMethodsResponse {
  const _$DeliveryPaymentMethodsResponseImpl(
      {final List<DeliveryPaymentMethod> deliveryPaymentMethods = const [],
      this.instructions = '',
      this.shopId})
      : _deliveryPaymentMethods = deliveryPaymentMethods,
        super._();

  final List<DeliveryPaymentMethod> _deliveryPaymentMethods;
  @override
  @JsonKey()
  List<DeliveryPaymentMethod> get deliveryPaymentMethods {
    if (_deliveryPaymentMethods is EqualUnmodifiableListView)
      return _deliveryPaymentMethods;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_deliveryPaymentMethods);
  }

  @override
  @JsonKey()
  final String instructions;
  @override
  final int? shopId;

  @override
  String toString() {
    return 'DeliveryPaymentMethodsResponse(deliveryPaymentMethods: $deliveryPaymentMethods, instructions: $instructions, shopId: $shopId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeliveryPaymentMethodsResponseImpl &&
            const DeepCollectionEquality().equals(
                other._deliveryPaymentMethods, _deliveryPaymentMethods) &&
            (identical(other.instructions, instructions) ||
                other.instructions == instructions) &&
            (identical(other.shopId, shopId) || other.shopId == shopId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_deliveryPaymentMethods),
      instructions,
      shopId);

  /// Create a copy of DeliveryPaymentMethodsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeliveryPaymentMethodsResponseImplCopyWith<
          _$DeliveryPaymentMethodsResponseImpl>
      get copyWith => __$$DeliveryPaymentMethodsResponseImplCopyWithImpl<
          _$DeliveryPaymentMethodsResponseImpl>(this, _$identity);
}

abstract class _DeliveryPaymentMethodsResponse
    extends DeliveryPaymentMethodsResponse {
  const factory _DeliveryPaymentMethodsResponse(
      {final List<DeliveryPaymentMethod> deliveryPaymentMethods,
      final String instructions,
      final int? shopId}) = _$DeliveryPaymentMethodsResponseImpl;
  const _DeliveryPaymentMethodsResponse._() : super._();

  @override
  List<DeliveryPaymentMethod> get deliveryPaymentMethods;
  @override
  String get instructions;
  @override
  int? get shopId;

  /// Create a copy of DeliveryPaymentMethodsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeliveryPaymentMethodsResponseImplCopyWith<
          _$DeliveryPaymentMethodsResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
