// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'delivery_payment_method.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DeliveryPaymentMethod {
  String? get tag => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  bool get supportsChange => throw _privateConstructorUsedError;
  double get maxChangeAmount => throw _privateConstructorUsedError;

  /// Create a copy of DeliveryPaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeliveryPaymentMethodCopyWith<DeliveryPaymentMethod> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeliveryPaymentMethodCopyWith<$Res> {
  factory $DeliveryPaymentMethodCopyWith(DeliveryPaymentMethod value,
          $Res Function(DeliveryPaymentMethod) then) =
      _$DeliveryPaymentMethodCopyWithImpl<$Res, DeliveryPaymentMethod>;
  @useResult
  $Res call(
      {String? tag,
      String? name,
      String? icon,
      String? description,
      bool supportsChange,
      double maxChangeAmount});
}

/// @nodoc
class _$DeliveryPaymentMethodCopyWithImpl<$Res,
        $Val extends DeliveryPaymentMethod>
    implements $DeliveryPaymentMethodCopyWith<$Res> {
  _$DeliveryPaymentMethodCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeliveryPaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tag = freezed,
    Object? name = freezed,
    Object? icon = freezed,
    Object? description = freezed,
    Object? supportsChange = null,
    Object? maxChangeAmount = null,
  }) {
    return _then(_value.copyWith(
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      supportsChange: null == supportsChange
          ? _value.supportsChange
          : supportsChange // ignore: cast_nullable_to_non_nullable
              as bool,
      maxChangeAmount: null == maxChangeAmount
          ? _value.maxChangeAmount
          : maxChangeAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeliveryPaymentMethodImplCopyWith<$Res>
    implements $DeliveryPaymentMethodCopyWith<$Res> {
  factory _$$DeliveryPaymentMethodImplCopyWith(
          _$DeliveryPaymentMethodImpl value,
          $Res Function(_$DeliveryPaymentMethodImpl) then) =
      __$$DeliveryPaymentMethodImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? tag,
      String? name,
      String? icon,
      String? description,
      bool supportsChange,
      double maxChangeAmount});
}

/// @nodoc
class __$$DeliveryPaymentMethodImplCopyWithImpl<$Res>
    extends _$DeliveryPaymentMethodCopyWithImpl<$Res,
        _$DeliveryPaymentMethodImpl>
    implements _$$DeliveryPaymentMethodImplCopyWith<$Res> {
  __$$DeliveryPaymentMethodImplCopyWithImpl(_$DeliveryPaymentMethodImpl _value,
      $Res Function(_$DeliveryPaymentMethodImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeliveryPaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tag = freezed,
    Object? name = freezed,
    Object? icon = freezed,
    Object? description = freezed,
    Object? supportsChange = null,
    Object? maxChangeAmount = null,
  }) {
    return _then(_$DeliveryPaymentMethodImpl(
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      supportsChange: null == supportsChange
          ? _value.supportsChange
          : supportsChange // ignore: cast_nullable_to_non_nullable
              as bool,
      maxChangeAmount: null == maxChangeAmount
          ? _value.maxChangeAmount
          : maxChangeAmount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$DeliveryPaymentMethodImpl extends _DeliveryPaymentMethod {
  const _$DeliveryPaymentMethodImpl(
      {this.tag,
      this.name,
      this.icon,
      this.description,
      this.supportsChange = false,
      this.maxChangeAmount = 0.0})
      : super._();

  @override
  final String? tag;
  @override
  final String? name;
  @override
  final String? icon;
  @override
  final String? description;
  @override
  @JsonKey()
  final bool supportsChange;
  @override
  @JsonKey()
  final double maxChangeAmount;

  @override
  String toString() {
    return 'DeliveryPaymentMethod(tag: $tag, name: $name, icon: $icon, description: $description, supportsChange: $supportsChange, maxChangeAmount: $maxChangeAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeliveryPaymentMethodImpl &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.supportsChange, supportsChange) ||
                other.supportsChange == supportsChange) &&
            (identical(other.maxChangeAmount, maxChangeAmount) ||
                other.maxChangeAmount == maxChangeAmount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, tag, name, icon, description,
      supportsChange, maxChangeAmount);

  /// Create a copy of DeliveryPaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeliveryPaymentMethodImplCopyWith<_$DeliveryPaymentMethodImpl>
      get copyWith => __$$DeliveryPaymentMethodImplCopyWithImpl<
          _$DeliveryPaymentMethodImpl>(this, _$identity);
}

abstract class _DeliveryPaymentMethod extends DeliveryPaymentMethod {
  const factory _DeliveryPaymentMethod(
      {final String? tag,
      final String? name,
      final String? icon,
      final String? description,
      final bool supportsChange,
      final double maxChangeAmount}) = _$DeliveryPaymentMethodImpl;
  const _DeliveryPaymentMethod._() : super._();

  @override
  String? get tag;
  @override
  String? get name;
  @override
  String? get icon;
  @override
  String? get description;
  @override
  bool get supportsChange;
  @override
  double get maxChangeAmount;

  /// Create a copy of DeliveryPaymentMethod
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeliveryPaymentMethodImplCopyWith<_$DeliveryPaymentMethodImpl>
      get copyWith => throw _privateConstructorUsedError;
}
