import 'package:freezed_annotation/freezed_annotation.dart';

part 'delivery_payment_method.freezed.dart';

@Freezed(toJson: false, fromJson: false)
class DeliveryPaymentMethod with _$DeliveryPaymentMethod {
  const factory DeliveryPaymentMethod({
    String? tag,
    String? name,
    String? icon,
    String? description,
    @Default(false) bool supportsChange,
    @Default(0.0) double maxChangeAmount,
  }) = _DeliveryPaymentMethod;

  factory DeliveryPaymentMethod.fromJson(Map<String, dynamic> json) =>
      DeliveryPaymentMethod(
        tag: json['tag'] as String?,
        name: json['name'] as String?,
        icon: json['icon'] as String?,
        description: json['description'] as String?,
        supportsChange: json['supportsChange'] as bool? ?? false,
        maxChangeAmount: (json['maxChangeAmount'] as num?)?.toDouble() ?? 0.0,
      );

  const DeliveryPaymentMethod._();
}
