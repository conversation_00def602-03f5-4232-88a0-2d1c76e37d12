// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HomeState {
  bool get isCategoryLoading => throw _privateConstructorUsedError;
  bool get isBannerLoading => throw _privateConstructorUsedError;
  bool get isShopLoading => throw _privateConstructorUsedError;
  bool get isRestaurantLoading => throw _privateConstructorUsedError;
  bool get isRestaurantNewLoading => throw _privateConstructorUsedError;
  bool get isStoryLoading => throw _privateConstructorUsedError;
  bool get isShopRecommendLoading => throw _privateConstructorUsedError;
  int get totalShops => throw _privateConstructorUsedError;
  int get selectIndexCategory => throw _privateConstructorUsedError;
  int get selectIndexSubCategory => throw _privateConstructorUsedError;
  int get isSelectCategoryLoading => throw _privateConstructorUsedError;
  AddressNewModel? get addressData => throw _privateConstructorUsedError;
  List<CategoryData> get categories => throw _privateConstructorUsedError;
  List<BannerData> get banners => throw _privateConstructorUsedError;
  List<BannerData> get ads => throw _privateConstructorUsedError;
  BannerData? get banner => throw _privateConstructorUsedError;
  List<ShopData> get shops => throw _privateConstructorUsedError;
  List<ShopData> get restaurant => throw _privateConstructorUsedError;
  List<ShopData> get newRestaurant => throw _privateConstructorUsedError;
  List<List<StoryModel?>?>? get story => throw _privateConstructorUsedError;
  List<ShopData> get shopsRecommend => throw _privateConstructorUsedError;
  List<ShopData> get filterShops => throw _privateConstructorUsedError;
  List<ShopData> get filterMarket => throw _privateConstructorUsedError;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeStateCopyWith<HomeState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeStateCopyWith<$Res> {
  factory $HomeStateCopyWith(HomeState value, $Res Function(HomeState) then) =
      _$HomeStateCopyWithImpl<$Res, HomeState>;
  @useResult
  $Res call(
      {bool isCategoryLoading,
      bool isBannerLoading,
      bool isShopLoading,
      bool isRestaurantLoading,
      bool isRestaurantNewLoading,
      bool isStoryLoading,
      bool isShopRecommendLoading,
      int totalShops,
      int selectIndexCategory,
      int selectIndexSubCategory,
      int isSelectCategoryLoading,
      AddressNewModel? addressData,
      List<CategoryData> categories,
      List<BannerData> banners,
      List<BannerData> ads,
      BannerData? banner,
      List<ShopData> shops,
      List<ShopData> restaurant,
      List<ShopData> newRestaurant,
      List<List<StoryModel?>?>? story,
      List<ShopData> shopsRecommend,
      List<ShopData> filterShops,
      List<ShopData> filterMarket});
}

/// @nodoc
class _$HomeStateCopyWithImpl<$Res, $Val extends HomeState>
    implements $HomeStateCopyWith<$Res> {
  _$HomeStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCategoryLoading = null,
    Object? isBannerLoading = null,
    Object? isShopLoading = null,
    Object? isRestaurantLoading = null,
    Object? isRestaurantNewLoading = null,
    Object? isStoryLoading = null,
    Object? isShopRecommendLoading = null,
    Object? totalShops = null,
    Object? selectIndexCategory = null,
    Object? selectIndexSubCategory = null,
    Object? isSelectCategoryLoading = null,
    Object? addressData = freezed,
    Object? categories = null,
    Object? banners = null,
    Object? ads = null,
    Object? banner = freezed,
    Object? shops = null,
    Object? restaurant = null,
    Object? newRestaurant = null,
    Object? story = freezed,
    Object? shopsRecommend = null,
    Object? filterShops = null,
    Object? filterMarket = null,
  }) {
    return _then(_value.copyWith(
      isCategoryLoading: null == isCategoryLoading
          ? _value.isCategoryLoading
          : isCategoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isBannerLoading: null == isBannerLoading
          ? _value.isBannerLoading
          : isBannerLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShopLoading: null == isShopLoading
          ? _value.isShopLoading
          : isShopLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRestaurantLoading: null == isRestaurantLoading
          ? _value.isRestaurantLoading
          : isRestaurantLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRestaurantNewLoading: null == isRestaurantNewLoading
          ? _value.isRestaurantNewLoading
          : isRestaurantNewLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isStoryLoading: null == isStoryLoading
          ? _value.isStoryLoading
          : isStoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShopRecommendLoading: null == isShopRecommendLoading
          ? _value.isShopRecommendLoading
          : isShopRecommendLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      totalShops: null == totalShops
          ? _value.totalShops
          : totalShops // ignore: cast_nullable_to_non_nullable
              as int,
      selectIndexCategory: null == selectIndexCategory
          ? _value.selectIndexCategory
          : selectIndexCategory // ignore: cast_nullable_to_non_nullable
              as int,
      selectIndexSubCategory: null == selectIndexSubCategory
          ? _value.selectIndexSubCategory
          : selectIndexSubCategory // ignore: cast_nullable_to_non_nullable
              as int,
      isSelectCategoryLoading: null == isSelectCategoryLoading
          ? _value.isSelectCategoryLoading
          : isSelectCategoryLoading // ignore: cast_nullable_to_non_nullable
              as int,
      addressData: freezed == addressData
          ? _value.addressData
          : addressData // ignore: cast_nullable_to_non_nullable
              as AddressNewModel?,
      categories: null == categories
          ? _value.categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<CategoryData>,
      banners: null == banners
          ? _value.banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<BannerData>,
      ads: null == ads
          ? _value.ads
          : ads // ignore: cast_nullable_to_non_nullable
              as List<BannerData>,
      banner: freezed == banner
          ? _value.banner
          : banner // ignore: cast_nullable_to_non_nullable
              as BannerData?,
      shops: null == shops
          ? _value.shops
          : shops // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      restaurant: null == restaurant
          ? _value.restaurant
          : restaurant // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      newRestaurant: null == newRestaurant
          ? _value.newRestaurant
          : newRestaurant // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      story: freezed == story
          ? _value.story
          : story // ignore: cast_nullable_to_non_nullable
              as List<List<StoryModel?>?>?,
      shopsRecommend: null == shopsRecommend
          ? _value.shopsRecommend
          : shopsRecommend // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      filterShops: null == filterShops
          ? _value.filterShops
          : filterShops // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      filterMarket: null == filterMarket
          ? _value.filterMarket
          : filterMarket // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HomeStateImplCopyWith<$Res>
    implements $HomeStateCopyWith<$Res> {
  factory _$$HomeStateImplCopyWith(
          _$HomeStateImpl value, $Res Function(_$HomeStateImpl) then) =
      __$$HomeStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isCategoryLoading,
      bool isBannerLoading,
      bool isShopLoading,
      bool isRestaurantLoading,
      bool isRestaurantNewLoading,
      bool isStoryLoading,
      bool isShopRecommendLoading,
      int totalShops,
      int selectIndexCategory,
      int selectIndexSubCategory,
      int isSelectCategoryLoading,
      AddressNewModel? addressData,
      List<CategoryData> categories,
      List<BannerData> banners,
      List<BannerData> ads,
      BannerData? banner,
      List<ShopData> shops,
      List<ShopData> restaurant,
      List<ShopData> newRestaurant,
      List<List<StoryModel?>?>? story,
      List<ShopData> shopsRecommend,
      List<ShopData> filterShops,
      List<ShopData> filterMarket});
}

/// @nodoc
class __$$HomeStateImplCopyWithImpl<$Res>
    extends _$HomeStateCopyWithImpl<$Res, _$HomeStateImpl>
    implements _$$HomeStateImplCopyWith<$Res> {
  __$$HomeStateImplCopyWithImpl(
      _$HomeStateImpl _value, $Res Function(_$HomeStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCategoryLoading = null,
    Object? isBannerLoading = null,
    Object? isShopLoading = null,
    Object? isRestaurantLoading = null,
    Object? isRestaurantNewLoading = null,
    Object? isStoryLoading = null,
    Object? isShopRecommendLoading = null,
    Object? totalShops = null,
    Object? selectIndexCategory = null,
    Object? selectIndexSubCategory = null,
    Object? isSelectCategoryLoading = null,
    Object? addressData = freezed,
    Object? categories = null,
    Object? banners = null,
    Object? ads = null,
    Object? banner = freezed,
    Object? shops = null,
    Object? restaurant = null,
    Object? newRestaurant = null,
    Object? story = freezed,
    Object? shopsRecommend = null,
    Object? filterShops = null,
    Object? filterMarket = null,
  }) {
    return _then(_$HomeStateImpl(
      isCategoryLoading: null == isCategoryLoading
          ? _value.isCategoryLoading
          : isCategoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isBannerLoading: null == isBannerLoading
          ? _value.isBannerLoading
          : isBannerLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShopLoading: null == isShopLoading
          ? _value.isShopLoading
          : isShopLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRestaurantLoading: null == isRestaurantLoading
          ? _value.isRestaurantLoading
          : isRestaurantLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRestaurantNewLoading: null == isRestaurantNewLoading
          ? _value.isRestaurantNewLoading
          : isRestaurantNewLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isStoryLoading: null == isStoryLoading
          ? _value.isStoryLoading
          : isStoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShopRecommendLoading: null == isShopRecommendLoading
          ? _value.isShopRecommendLoading
          : isShopRecommendLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      totalShops: null == totalShops
          ? _value.totalShops
          : totalShops // ignore: cast_nullable_to_non_nullable
              as int,
      selectIndexCategory: null == selectIndexCategory
          ? _value.selectIndexCategory
          : selectIndexCategory // ignore: cast_nullable_to_non_nullable
              as int,
      selectIndexSubCategory: null == selectIndexSubCategory
          ? _value.selectIndexSubCategory
          : selectIndexSubCategory // ignore: cast_nullable_to_non_nullable
              as int,
      isSelectCategoryLoading: null == isSelectCategoryLoading
          ? _value.isSelectCategoryLoading
          : isSelectCategoryLoading // ignore: cast_nullable_to_non_nullable
              as int,
      addressData: freezed == addressData
          ? _value.addressData
          : addressData // ignore: cast_nullable_to_non_nullable
              as AddressNewModel?,
      categories: null == categories
          ? _value._categories
          : categories // ignore: cast_nullable_to_non_nullable
              as List<CategoryData>,
      banners: null == banners
          ? _value._banners
          : banners // ignore: cast_nullable_to_non_nullable
              as List<BannerData>,
      ads: null == ads
          ? _value._ads
          : ads // ignore: cast_nullable_to_non_nullable
              as List<BannerData>,
      banner: freezed == banner
          ? _value.banner
          : banner // ignore: cast_nullable_to_non_nullable
              as BannerData?,
      shops: null == shops
          ? _value._shops
          : shops // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      restaurant: null == restaurant
          ? _value._restaurant
          : restaurant // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      newRestaurant: null == newRestaurant
          ? _value._newRestaurant
          : newRestaurant // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      story: freezed == story
          ? _value._story
          : story // ignore: cast_nullable_to_non_nullable
              as List<List<StoryModel?>?>?,
      shopsRecommend: null == shopsRecommend
          ? _value._shopsRecommend
          : shopsRecommend // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      filterShops: null == filterShops
          ? _value._filterShops
          : filterShops // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      filterMarket: null == filterMarket
          ? _value._filterMarket
          : filterMarket // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
    ));
  }
}

/// @nodoc

class _$HomeStateImpl extends _HomeState {
  const _$HomeStateImpl(
      {this.isCategoryLoading = true,
      this.isBannerLoading = true,
      this.isShopLoading = true,
      this.isRestaurantLoading = true,
      this.isRestaurantNewLoading = true,
      this.isStoryLoading = true,
      this.isShopRecommendLoading = true,
      this.totalShops = -1,
      this.selectIndexCategory = -1,
      this.selectIndexSubCategory = -1,
      this.isSelectCategoryLoading = 0,
      this.addressData = null,
      final List<CategoryData> categories = const [],
      final List<BannerData> banners = const [],
      final List<BannerData> ads = const [],
      this.banner = null,
      final List<ShopData> shops = const [],
      final List<ShopData> restaurant = const [],
      final List<ShopData> newRestaurant = const [],
      final List<List<StoryModel?>?>? story = const [],
      final List<ShopData> shopsRecommend = const [],
      final List<ShopData> filterShops = const [],
      final List<ShopData> filterMarket = const []})
      : _categories = categories,
        _banners = banners,
        _ads = ads,
        _shops = shops,
        _restaurant = restaurant,
        _newRestaurant = newRestaurant,
        _story = story,
        _shopsRecommend = shopsRecommend,
        _filterShops = filterShops,
        _filterMarket = filterMarket,
        super._();

  @override
  @JsonKey()
  final bool isCategoryLoading;
  @override
  @JsonKey()
  final bool isBannerLoading;
  @override
  @JsonKey()
  final bool isShopLoading;
  @override
  @JsonKey()
  final bool isRestaurantLoading;
  @override
  @JsonKey()
  final bool isRestaurantNewLoading;
  @override
  @JsonKey()
  final bool isStoryLoading;
  @override
  @JsonKey()
  final bool isShopRecommendLoading;
  @override
  @JsonKey()
  final int totalShops;
  @override
  @JsonKey()
  final int selectIndexCategory;
  @override
  @JsonKey()
  final int selectIndexSubCategory;
  @override
  @JsonKey()
  final int isSelectCategoryLoading;
  @override
  @JsonKey()
  final AddressNewModel? addressData;
  final List<CategoryData> _categories;
  @override
  @JsonKey()
  List<CategoryData> get categories {
    if (_categories is EqualUnmodifiableListView) return _categories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categories);
  }

  final List<BannerData> _banners;
  @override
  @JsonKey()
  List<BannerData> get banners {
    if (_banners is EqualUnmodifiableListView) return _banners;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_banners);
  }

  final List<BannerData> _ads;
  @override
  @JsonKey()
  List<BannerData> get ads {
    if (_ads is EqualUnmodifiableListView) return _ads;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_ads);
  }

  @override
  @JsonKey()
  final BannerData? banner;
  final List<ShopData> _shops;
  @override
  @JsonKey()
  List<ShopData> get shops {
    if (_shops is EqualUnmodifiableListView) return _shops;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_shops);
  }

  final List<ShopData> _restaurant;
  @override
  @JsonKey()
  List<ShopData> get restaurant {
    if (_restaurant is EqualUnmodifiableListView) return _restaurant;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_restaurant);
  }

  final List<ShopData> _newRestaurant;
  @override
  @JsonKey()
  List<ShopData> get newRestaurant {
    if (_newRestaurant is EqualUnmodifiableListView) return _newRestaurant;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_newRestaurant);
  }

  final List<List<StoryModel?>?>? _story;
  @override
  @JsonKey()
  List<List<StoryModel?>?>? get story {
    final value = _story;
    if (value == null) return null;
    if (_story is EqualUnmodifiableListView) return _story;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ShopData> _shopsRecommend;
  @override
  @JsonKey()
  List<ShopData> get shopsRecommend {
    if (_shopsRecommend is EqualUnmodifiableListView) return _shopsRecommend;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_shopsRecommend);
  }

  final List<ShopData> _filterShops;
  @override
  @JsonKey()
  List<ShopData> get filterShops {
    if (_filterShops is EqualUnmodifiableListView) return _filterShops;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filterShops);
  }

  final List<ShopData> _filterMarket;
  @override
  @JsonKey()
  List<ShopData> get filterMarket {
    if (_filterMarket is EqualUnmodifiableListView) return _filterMarket;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filterMarket);
  }

  @override
  String toString() {
    return 'HomeState(isCategoryLoading: $isCategoryLoading, isBannerLoading: $isBannerLoading, isShopLoading: $isShopLoading, isRestaurantLoading: $isRestaurantLoading, isRestaurantNewLoading: $isRestaurantNewLoading, isStoryLoading: $isStoryLoading, isShopRecommendLoading: $isShopRecommendLoading, totalShops: $totalShops, selectIndexCategory: $selectIndexCategory, selectIndexSubCategory: $selectIndexSubCategory, isSelectCategoryLoading: $isSelectCategoryLoading, addressData: $addressData, categories: $categories, banners: $banners, ads: $ads, banner: $banner, shops: $shops, restaurant: $restaurant, newRestaurant: $newRestaurant, story: $story, shopsRecommend: $shopsRecommend, filterShops: $filterShops, filterMarket: $filterMarket)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeStateImpl &&
            (identical(other.isCategoryLoading, isCategoryLoading) ||
                other.isCategoryLoading == isCategoryLoading) &&
            (identical(other.isBannerLoading, isBannerLoading) ||
                other.isBannerLoading == isBannerLoading) &&
            (identical(other.isShopLoading, isShopLoading) ||
                other.isShopLoading == isShopLoading) &&
            (identical(other.isRestaurantLoading, isRestaurantLoading) ||
                other.isRestaurantLoading == isRestaurantLoading) &&
            (identical(other.isRestaurantNewLoading, isRestaurantNewLoading) ||
                other.isRestaurantNewLoading == isRestaurantNewLoading) &&
            (identical(other.isStoryLoading, isStoryLoading) ||
                other.isStoryLoading == isStoryLoading) &&
            (identical(other.isShopRecommendLoading, isShopRecommendLoading) ||
                other.isShopRecommendLoading == isShopRecommendLoading) &&
            (identical(other.totalShops, totalShops) ||
                other.totalShops == totalShops) &&
            (identical(other.selectIndexCategory, selectIndexCategory) ||
                other.selectIndexCategory == selectIndexCategory) &&
            (identical(other.selectIndexSubCategory, selectIndexSubCategory) ||
                other.selectIndexSubCategory == selectIndexSubCategory) &&
            (identical(
                    other.isSelectCategoryLoading, isSelectCategoryLoading) ||
                other.isSelectCategoryLoading == isSelectCategoryLoading) &&
            (identical(other.addressData, addressData) ||
                other.addressData == addressData) &&
            const DeepCollectionEquality()
                .equals(other._categories, _categories) &&
            const DeepCollectionEquality().equals(other._banners, _banners) &&
            const DeepCollectionEquality().equals(other._ads, _ads) &&
            (identical(other.banner, banner) || other.banner == banner) &&
            const DeepCollectionEquality().equals(other._shops, _shops) &&
            const DeepCollectionEquality()
                .equals(other._restaurant, _restaurant) &&
            const DeepCollectionEquality()
                .equals(other._newRestaurant, _newRestaurant) &&
            const DeepCollectionEquality().equals(other._story, _story) &&
            const DeepCollectionEquality()
                .equals(other._shopsRecommend, _shopsRecommend) &&
            const DeepCollectionEquality()
                .equals(other._filterShops, _filterShops) &&
            const DeepCollectionEquality()
                .equals(other._filterMarket, _filterMarket));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        isCategoryLoading,
        isBannerLoading,
        isShopLoading,
        isRestaurantLoading,
        isRestaurantNewLoading,
        isStoryLoading,
        isShopRecommendLoading,
        totalShops,
        selectIndexCategory,
        selectIndexSubCategory,
        isSelectCategoryLoading,
        addressData,
        const DeepCollectionEquality().hash(_categories),
        const DeepCollectionEquality().hash(_banners),
        const DeepCollectionEquality().hash(_ads),
        banner,
        const DeepCollectionEquality().hash(_shops),
        const DeepCollectionEquality().hash(_restaurant),
        const DeepCollectionEquality().hash(_newRestaurant),
        const DeepCollectionEquality().hash(_story),
        const DeepCollectionEquality().hash(_shopsRecommend),
        const DeepCollectionEquality().hash(_filterShops),
        const DeepCollectionEquality().hash(_filterMarket)
      ]);

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      __$$HomeStateImplCopyWithImpl<_$HomeStateImpl>(this, _$identity);
}

abstract class _HomeState extends HomeState {
  const factory _HomeState(
      {final bool isCategoryLoading,
      final bool isBannerLoading,
      final bool isShopLoading,
      final bool isRestaurantLoading,
      final bool isRestaurantNewLoading,
      final bool isStoryLoading,
      final bool isShopRecommendLoading,
      final int totalShops,
      final int selectIndexCategory,
      final int selectIndexSubCategory,
      final int isSelectCategoryLoading,
      final AddressNewModel? addressData,
      final List<CategoryData> categories,
      final List<BannerData> banners,
      final List<BannerData> ads,
      final BannerData? banner,
      final List<ShopData> shops,
      final List<ShopData> restaurant,
      final List<ShopData> newRestaurant,
      final List<List<StoryModel?>?>? story,
      final List<ShopData> shopsRecommend,
      final List<ShopData> filterShops,
      final List<ShopData> filterMarket}) = _$HomeStateImpl;
  const _HomeState._() : super._();

  @override
  bool get isCategoryLoading;
  @override
  bool get isBannerLoading;
  @override
  bool get isShopLoading;
  @override
  bool get isRestaurantLoading;
  @override
  bool get isRestaurantNewLoading;
  @override
  bool get isStoryLoading;
  @override
  bool get isShopRecommendLoading;
  @override
  int get totalShops;
  @override
  int get selectIndexCategory;
  @override
  int get selectIndexSubCategory;
  @override
  int get isSelectCategoryLoading;
  @override
  AddressNewModel? get addressData;
  @override
  List<CategoryData> get categories;
  @override
  List<BannerData> get banners;
  @override
  List<BannerData> get ads;
  @override
  BannerData? get banner;
  @override
  List<ShopData> get shops;
  @override
  List<ShopData> get restaurant;
  @override
  List<ShopData> get newRestaurant;
  @override
  List<List<StoryModel?>?>? get story;
  @override
  List<ShopData> get shopsRecommend;
  @override
  List<ShopData> get filterShops;
  @override
  List<ShopData> get filterMarket;

  /// Create a copy of HomeState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeStateImplCopyWith<_$HomeStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
