// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'filter_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FilterState {
  FilterModel? get filterModel => throw _privateConstructorUsedError;
  bool get freeDelivery => throw _privateConstructorUsedError;
  bool get deals => throw _privateConstructorUsedError;
  bool get open => throw _privateConstructorUsedError;
  int get shopCount => throw _privateConstructorUsedError;
  double get endPrice => throw _privateConstructorUsedError;
  double get startPrice => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isTagLoading => throw _privateConstructorUsedError;
  bool get isShopLoading => throw _privateConstructorUsedError;
  bool get isRestaurantLoading => throw _privateConstructorUsedError;
  RangeValues get rangeValues => throw _privateConstructorUsedError;
  List<ShopData> get shops => throw _privateConstructorUsedError;
  List<TakeModel> get tags => throw _privateConstructorUsedError;
  List<int> get prices => throw _privateConstructorUsedError;
  List<ShopData> get restaurant => throw _privateConstructorUsedError;

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FilterStateCopyWith<FilterState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FilterStateCopyWith<$Res> {
  factory $FilterStateCopyWith(
          FilterState value, $Res Function(FilterState) then) =
      _$FilterStateCopyWithImpl<$Res, FilterState>;
  @useResult
  $Res call(
      {FilterModel? filterModel,
      bool freeDelivery,
      bool deals,
      bool open,
      int shopCount,
      double endPrice,
      double startPrice,
      bool isLoading,
      bool isTagLoading,
      bool isShopLoading,
      bool isRestaurantLoading,
      RangeValues rangeValues,
      List<ShopData> shops,
      List<TakeModel> tags,
      List<int> prices,
      List<ShopData> restaurant});
}

/// @nodoc
class _$FilterStateCopyWithImpl<$Res, $Val extends FilterState>
    implements $FilterStateCopyWith<$Res> {
  _$FilterStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filterModel = freezed,
    Object? freeDelivery = null,
    Object? deals = null,
    Object? open = null,
    Object? shopCount = null,
    Object? endPrice = null,
    Object? startPrice = null,
    Object? isLoading = null,
    Object? isTagLoading = null,
    Object? isShopLoading = null,
    Object? isRestaurantLoading = null,
    Object? rangeValues = null,
    Object? shops = null,
    Object? tags = null,
    Object? prices = null,
    Object? restaurant = null,
  }) {
    return _then(_value.copyWith(
      filterModel: freezed == filterModel
          ? _value.filterModel
          : filterModel // ignore: cast_nullable_to_non_nullable
              as FilterModel?,
      freeDelivery: null == freeDelivery
          ? _value.freeDelivery
          : freeDelivery // ignore: cast_nullable_to_non_nullable
              as bool,
      deals: null == deals
          ? _value.deals
          : deals // ignore: cast_nullable_to_non_nullable
              as bool,
      open: null == open
          ? _value.open
          : open // ignore: cast_nullable_to_non_nullable
              as bool,
      shopCount: null == shopCount
          ? _value.shopCount
          : shopCount // ignore: cast_nullable_to_non_nullable
              as int,
      endPrice: null == endPrice
          ? _value.endPrice
          : endPrice // ignore: cast_nullable_to_non_nullable
              as double,
      startPrice: null == startPrice
          ? _value.startPrice
          : startPrice // ignore: cast_nullable_to_non_nullable
              as double,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isTagLoading: null == isTagLoading
          ? _value.isTagLoading
          : isTagLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShopLoading: null == isShopLoading
          ? _value.isShopLoading
          : isShopLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRestaurantLoading: null == isRestaurantLoading
          ? _value.isRestaurantLoading
          : isRestaurantLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      rangeValues: null == rangeValues
          ? _value.rangeValues
          : rangeValues // ignore: cast_nullable_to_non_nullable
              as RangeValues,
      shops: null == shops
          ? _value.shops
          : shops // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<TakeModel>,
      prices: null == prices
          ? _value.prices
          : prices // ignore: cast_nullable_to_non_nullable
              as List<int>,
      restaurant: null == restaurant
          ? _value.restaurant
          : restaurant // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FilterStateImplCopyWith<$Res>
    implements $FilterStateCopyWith<$Res> {
  factory _$$FilterStateImplCopyWith(
          _$FilterStateImpl value, $Res Function(_$FilterStateImpl) then) =
      __$$FilterStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {FilterModel? filterModel,
      bool freeDelivery,
      bool deals,
      bool open,
      int shopCount,
      double endPrice,
      double startPrice,
      bool isLoading,
      bool isTagLoading,
      bool isShopLoading,
      bool isRestaurantLoading,
      RangeValues rangeValues,
      List<ShopData> shops,
      List<TakeModel> tags,
      List<int> prices,
      List<ShopData> restaurant});
}

/// @nodoc
class __$$FilterStateImplCopyWithImpl<$Res>
    extends _$FilterStateCopyWithImpl<$Res, _$FilterStateImpl>
    implements _$$FilterStateImplCopyWith<$Res> {
  __$$FilterStateImplCopyWithImpl(
      _$FilterStateImpl _value, $Res Function(_$FilterStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filterModel = freezed,
    Object? freeDelivery = null,
    Object? deals = null,
    Object? open = null,
    Object? shopCount = null,
    Object? endPrice = null,
    Object? startPrice = null,
    Object? isLoading = null,
    Object? isTagLoading = null,
    Object? isShopLoading = null,
    Object? isRestaurantLoading = null,
    Object? rangeValues = null,
    Object? shops = null,
    Object? tags = null,
    Object? prices = null,
    Object? restaurant = null,
  }) {
    return _then(_$FilterStateImpl(
      filterModel: freezed == filterModel
          ? _value.filterModel
          : filterModel // ignore: cast_nullable_to_non_nullable
              as FilterModel?,
      freeDelivery: null == freeDelivery
          ? _value.freeDelivery
          : freeDelivery // ignore: cast_nullable_to_non_nullable
              as bool,
      deals: null == deals
          ? _value.deals
          : deals // ignore: cast_nullable_to_non_nullable
              as bool,
      open: null == open
          ? _value.open
          : open // ignore: cast_nullable_to_non_nullable
              as bool,
      shopCount: null == shopCount
          ? _value.shopCount
          : shopCount // ignore: cast_nullable_to_non_nullable
              as int,
      endPrice: null == endPrice
          ? _value.endPrice
          : endPrice // ignore: cast_nullable_to_non_nullable
              as double,
      startPrice: null == startPrice
          ? _value.startPrice
          : startPrice // ignore: cast_nullable_to_non_nullable
              as double,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isTagLoading: null == isTagLoading
          ? _value.isTagLoading
          : isTagLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShopLoading: null == isShopLoading
          ? _value.isShopLoading
          : isShopLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRestaurantLoading: null == isRestaurantLoading
          ? _value.isRestaurantLoading
          : isRestaurantLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      rangeValues: null == rangeValues
          ? _value.rangeValues
          : rangeValues // ignore: cast_nullable_to_non_nullable
              as RangeValues,
      shops: null == shops
          ? _value._shops
          : shops // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<TakeModel>,
      prices: null == prices
          ? _value._prices
          : prices // ignore: cast_nullable_to_non_nullable
              as List<int>,
      restaurant: null == restaurant
          ? _value._restaurant
          : restaurant // ignore: cast_nullable_to_non_nullable
              as List<ShopData>,
    ));
  }
}

/// @nodoc

class _$FilterStateImpl extends _FilterState {
  const _$FilterStateImpl(
      {this.filterModel = null,
      this.freeDelivery = false,
      this.deals = false,
      this.open = true,
      this.shopCount = 0,
      this.endPrice = 100,
      this.startPrice = 1,
      this.isLoading = false,
      this.isTagLoading = false,
      this.isShopLoading = true,
      this.isRestaurantLoading = true,
      this.rangeValues = const RangeValues(1, 100),
      final List<ShopData> shops = const [],
      final List<TakeModel> tags = const [],
      final List<int> prices = const [],
      final List<ShopData> restaurant = const []})
      : _shops = shops,
        _tags = tags,
        _prices = prices,
        _restaurant = restaurant,
        super._();

  @override
  @JsonKey()
  final FilterModel? filterModel;
  @override
  @JsonKey()
  final bool freeDelivery;
  @override
  @JsonKey()
  final bool deals;
  @override
  @JsonKey()
  final bool open;
  @override
  @JsonKey()
  final int shopCount;
  @override
  @JsonKey()
  final double endPrice;
  @override
  @JsonKey()
  final double startPrice;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isTagLoading;
  @override
  @JsonKey()
  final bool isShopLoading;
  @override
  @JsonKey()
  final bool isRestaurantLoading;
  @override
  @JsonKey()
  final RangeValues rangeValues;
  final List<ShopData> _shops;
  @override
  @JsonKey()
  List<ShopData> get shops {
    if (_shops is EqualUnmodifiableListView) return _shops;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_shops);
  }

  final List<TakeModel> _tags;
  @override
  @JsonKey()
  List<TakeModel> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  final List<int> _prices;
  @override
  @JsonKey()
  List<int> get prices {
    if (_prices is EqualUnmodifiableListView) return _prices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_prices);
  }

  final List<ShopData> _restaurant;
  @override
  @JsonKey()
  List<ShopData> get restaurant {
    if (_restaurant is EqualUnmodifiableListView) return _restaurant;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_restaurant);
  }

  @override
  String toString() {
    return 'FilterState(filterModel: $filterModel, freeDelivery: $freeDelivery, deals: $deals, open: $open, shopCount: $shopCount, endPrice: $endPrice, startPrice: $startPrice, isLoading: $isLoading, isTagLoading: $isTagLoading, isShopLoading: $isShopLoading, isRestaurantLoading: $isRestaurantLoading, rangeValues: $rangeValues, shops: $shops, tags: $tags, prices: $prices, restaurant: $restaurant)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterStateImpl &&
            (identical(other.filterModel, filterModel) ||
                other.filterModel == filterModel) &&
            (identical(other.freeDelivery, freeDelivery) ||
                other.freeDelivery == freeDelivery) &&
            (identical(other.deals, deals) || other.deals == deals) &&
            (identical(other.open, open) || other.open == open) &&
            (identical(other.shopCount, shopCount) ||
                other.shopCount == shopCount) &&
            (identical(other.endPrice, endPrice) ||
                other.endPrice == endPrice) &&
            (identical(other.startPrice, startPrice) ||
                other.startPrice == startPrice) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isTagLoading, isTagLoading) ||
                other.isTagLoading == isTagLoading) &&
            (identical(other.isShopLoading, isShopLoading) ||
                other.isShopLoading == isShopLoading) &&
            (identical(other.isRestaurantLoading, isRestaurantLoading) ||
                other.isRestaurantLoading == isRestaurantLoading) &&
            (identical(other.rangeValues, rangeValues) ||
                other.rangeValues == rangeValues) &&
            const DeepCollectionEquality().equals(other._shops, _shops) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            const DeepCollectionEquality().equals(other._prices, _prices) &&
            const DeepCollectionEquality()
                .equals(other._restaurant, _restaurant));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      filterModel,
      freeDelivery,
      deals,
      open,
      shopCount,
      endPrice,
      startPrice,
      isLoading,
      isTagLoading,
      isShopLoading,
      isRestaurantLoading,
      rangeValues,
      const DeepCollectionEquality().hash(_shops),
      const DeepCollectionEquality().hash(_tags),
      const DeepCollectionEquality().hash(_prices),
      const DeepCollectionEquality().hash(_restaurant));

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterStateImplCopyWith<_$FilterStateImpl> get copyWith =>
      __$$FilterStateImplCopyWithImpl<_$FilterStateImpl>(this, _$identity);
}

abstract class _FilterState extends FilterState {
  const factory _FilterState(
      {final FilterModel? filterModel,
      final bool freeDelivery,
      final bool deals,
      final bool open,
      final int shopCount,
      final double endPrice,
      final double startPrice,
      final bool isLoading,
      final bool isTagLoading,
      final bool isShopLoading,
      final bool isRestaurantLoading,
      final RangeValues rangeValues,
      final List<ShopData> shops,
      final List<TakeModel> tags,
      final List<int> prices,
      final List<ShopData> restaurant}) = _$FilterStateImpl;
  const _FilterState._() : super._();

  @override
  FilterModel? get filterModel;
  @override
  bool get freeDelivery;
  @override
  bool get deals;
  @override
  bool get open;
  @override
  int get shopCount;
  @override
  double get endPrice;
  @override
  double get startPrice;
  @override
  bool get isLoading;
  @override
  bool get isTagLoading;
  @override
  bool get isShopLoading;
  @override
  bool get isRestaurantLoading;
  @override
  RangeValues get rangeValues;
  @override
  List<ShopData> get shops;
  @override
  List<TakeModel> get tags;
  @override
  List<int> get prices;
  @override
  List<ShopData> get restaurant;

  /// Create a copy of FilterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FilterStateImplCopyWith<_$FilterStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
