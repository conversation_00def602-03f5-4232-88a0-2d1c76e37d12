// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shop_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ShopState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isFilterLoading => throw _privateConstructorUsedError;
  bool get isCategoryLoading => throw _privateConstructorUsedError;
  bool get isPopularLoading => throw _privateConstructorUsedError;
  bool get isProductLoading => throw _privateConstructorUsedError;
  bool get isProductCategoryLoading => throw _privateConstructorUsedError;
  bool get isPopularProduct => throw _privateConstructorUsedError;
  bool get isLike => throw _privateConstructorUsedError;
  bool get showWeekTime => throw _privateConstructorUsedError;
  bool get showBranch => throw _privateConstructorUsedError;
  bool get isMapLoading => throw _privateConstructorUsedError;
  bool get isGroupOrder => throw _privateConstructorUsedError;
  bool get isJoinOrder => throw _privateConstructorUsedError;
  bool get isSearchEnabled => throw _privateConstructorUsedError;
  bool get isTodayWorkingDay => throw _privateConstructorUsedError;
  bool get isTomorrowWorkingDay => throw _privateConstructorUsedError;
  bool get isNestedScrollDisabled => throw _privateConstructorUsedError;
  String get userUuid => throw _privateConstructorUsedError;
  String get searchText => throw _privateConstructorUsedError;
  TimeOfDay get startTodayTime => throw _privateConstructorUsedError;
  TimeOfDay get endTodayTime => throw _privateConstructorUsedError;
  int get currentIndex => throw _privateConstructorUsedError;
  int get subCategoryIndex => throw _privateConstructorUsedError;
  Set<Marker> get shopMarkers => throw _privateConstructorUsedError;
  List<LatLng> get polylineCoordinates => throw _privateConstructorUsedError;
  ShopData? get shopData =>
      throw _privateConstructorUsedError; // @Default([]) List<ProductData> products,
// @Default([]) List<ProductData> popularProducts,
  List<ProductData> get categoryProducts => throw _privateConstructorUsedError;
  List<All> get allData => throw _privateConstructorUsedError;
  List<CategoryData>? get category => throw _privateConstructorUsedError;
  List<BrandData>? get brands => throw _privateConstructorUsedError;
  List<BranchModel>? get branches => throw _privateConstructorUsedError;
  List<int> get brandIds => throw _privateConstructorUsedError;
  int get sortIndex => throw _privateConstructorUsedError;

  /// Create a copy of ShopState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShopStateCopyWith<ShopState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShopStateCopyWith<$Res> {
  factory $ShopStateCopyWith(ShopState value, $Res Function(ShopState) then) =
      _$ShopStateCopyWithImpl<$Res, ShopState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isFilterLoading,
      bool isCategoryLoading,
      bool isPopularLoading,
      bool isProductLoading,
      bool isProductCategoryLoading,
      bool isPopularProduct,
      bool isLike,
      bool showWeekTime,
      bool showBranch,
      bool isMapLoading,
      bool isGroupOrder,
      bool isJoinOrder,
      bool isSearchEnabled,
      bool isTodayWorkingDay,
      bool isTomorrowWorkingDay,
      bool isNestedScrollDisabled,
      String userUuid,
      String searchText,
      TimeOfDay startTodayTime,
      TimeOfDay endTodayTime,
      int currentIndex,
      int subCategoryIndex,
      Set<Marker> shopMarkers,
      List<LatLng> polylineCoordinates,
      ShopData? shopData,
      List<ProductData> categoryProducts,
      List<All> allData,
      List<CategoryData>? category,
      List<BrandData>? brands,
      List<BranchModel>? branches,
      List<int> brandIds,
      int sortIndex});
}

/// @nodoc
class _$ShopStateCopyWithImpl<$Res, $Val extends ShopState>
    implements $ShopStateCopyWith<$Res> {
  _$ShopStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShopState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isFilterLoading = null,
    Object? isCategoryLoading = null,
    Object? isPopularLoading = null,
    Object? isProductLoading = null,
    Object? isProductCategoryLoading = null,
    Object? isPopularProduct = null,
    Object? isLike = null,
    Object? showWeekTime = null,
    Object? showBranch = null,
    Object? isMapLoading = null,
    Object? isGroupOrder = null,
    Object? isJoinOrder = null,
    Object? isSearchEnabled = null,
    Object? isTodayWorkingDay = null,
    Object? isTomorrowWorkingDay = null,
    Object? isNestedScrollDisabled = null,
    Object? userUuid = null,
    Object? searchText = null,
    Object? startTodayTime = null,
    Object? endTodayTime = null,
    Object? currentIndex = null,
    Object? subCategoryIndex = null,
    Object? shopMarkers = null,
    Object? polylineCoordinates = null,
    Object? shopData = freezed,
    Object? categoryProducts = null,
    Object? allData = null,
    Object? category = freezed,
    Object? brands = freezed,
    Object? branches = freezed,
    Object? brandIds = null,
    Object? sortIndex = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isFilterLoading: null == isFilterLoading
          ? _value.isFilterLoading
          : isFilterLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCategoryLoading: null == isCategoryLoading
          ? _value.isCategoryLoading
          : isCategoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isPopularLoading: null == isPopularLoading
          ? _value.isPopularLoading
          : isPopularLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isProductLoading: null == isProductLoading
          ? _value.isProductLoading
          : isProductLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isProductCategoryLoading: null == isProductCategoryLoading
          ? _value.isProductCategoryLoading
          : isProductCategoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isPopularProduct: null == isPopularProduct
          ? _value.isPopularProduct
          : isPopularProduct // ignore: cast_nullable_to_non_nullable
              as bool,
      isLike: null == isLike
          ? _value.isLike
          : isLike // ignore: cast_nullable_to_non_nullable
              as bool,
      showWeekTime: null == showWeekTime
          ? _value.showWeekTime
          : showWeekTime // ignore: cast_nullable_to_non_nullable
              as bool,
      showBranch: null == showBranch
          ? _value.showBranch
          : showBranch // ignore: cast_nullable_to_non_nullable
              as bool,
      isMapLoading: null == isMapLoading
          ? _value.isMapLoading
          : isMapLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isGroupOrder: null == isGroupOrder
          ? _value.isGroupOrder
          : isGroupOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      isJoinOrder: null == isJoinOrder
          ? _value.isJoinOrder
          : isJoinOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      isSearchEnabled: null == isSearchEnabled
          ? _value.isSearchEnabled
          : isSearchEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isTodayWorkingDay: null == isTodayWorkingDay
          ? _value.isTodayWorkingDay
          : isTodayWorkingDay // ignore: cast_nullable_to_non_nullable
              as bool,
      isTomorrowWorkingDay: null == isTomorrowWorkingDay
          ? _value.isTomorrowWorkingDay
          : isTomorrowWorkingDay // ignore: cast_nullable_to_non_nullable
              as bool,
      isNestedScrollDisabled: null == isNestedScrollDisabled
          ? _value.isNestedScrollDisabled
          : isNestedScrollDisabled // ignore: cast_nullable_to_non_nullable
              as bool,
      userUuid: null == userUuid
          ? _value.userUuid
          : userUuid // ignore: cast_nullable_to_non_nullable
              as String,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      startTodayTime: null == startTodayTime
          ? _value.startTodayTime
          : startTodayTime // ignore: cast_nullable_to_non_nullable
              as TimeOfDay,
      endTodayTime: null == endTodayTime
          ? _value.endTodayTime
          : endTodayTime // ignore: cast_nullable_to_non_nullable
              as TimeOfDay,
      currentIndex: null == currentIndex
          ? _value.currentIndex
          : currentIndex // ignore: cast_nullable_to_non_nullable
              as int,
      subCategoryIndex: null == subCategoryIndex
          ? _value.subCategoryIndex
          : subCategoryIndex // ignore: cast_nullable_to_non_nullable
              as int,
      shopMarkers: null == shopMarkers
          ? _value.shopMarkers
          : shopMarkers // ignore: cast_nullable_to_non_nullable
              as Set<Marker>,
      polylineCoordinates: null == polylineCoordinates
          ? _value.polylineCoordinates
          : polylineCoordinates // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
      shopData: freezed == shopData
          ? _value.shopData
          : shopData // ignore: cast_nullable_to_non_nullable
              as ShopData?,
      categoryProducts: null == categoryProducts
          ? _value.categoryProducts
          : categoryProducts // ignore: cast_nullable_to_non_nullable
              as List<ProductData>,
      allData: null == allData
          ? _value.allData
          : allData // ignore: cast_nullable_to_non_nullable
              as List<All>,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as List<CategoryData>?,
      brands: freezed == brands
          ? _value.brands
          : brands // ignore: cast_nullable_to_non_nullable
              as List<BrandData>?,
      branches: freezed == branches
          ? _value.branches
          : branches // ignore: cast_nullable_to_non_nullable
              as List<BranchModel>?,
      brandIds: null == brandIds
          ? _value.brandIds
          : brandIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
      sortIndex: null == sortIndex
          ? _value.sortIndex
          : sortIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShopStateImplCopyWith<$Res>
    implements $ShopStateCopyWith<$Res> {
  factory _$$ShopStateImplCopyWith(
          _$ShopStateImpl value, $Res Function(_$ShopStateImpl) then) =
      __$$ShopStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isFilterLoading,
      bool isCategoryLoading,
      bool isPopularLoading,
      bool isProductLoading,
      bool isProductCategoryLoading,
      bool isPopularProduct,
      bool isLike,
      bool showWeekTime,
      bool showBranch,
      bool isMapLoading,
      bool isGroupOrder,
      bool isJoinOrder,
      bool isSearchEnabled,
      bool isTodayWorkingDay,
      bool isTomorrowWorkingDay,
      bool isNestedScrollDisabled,
      String userUuid,
      String searchText,
      TimeOfDay startTodayTime,
      TimeOfDay endTodayTime,
      int currentIndex,
      int subCategoryIndex,
      Set<Marker> shopMarkers,
      List<LatLng> polylineCoordinates,
      ShopData? shopData,
      List<ProductData> categoryProducts,
      List<All> allData,
      List<CategoryData>? category,
      List<BrandData>? brands,
      List<BranchModel>? branches,
      List<int> brandIds,
      int sortIndex});
}

/// @nodoc
class __$$ShopStateImplCopyWithImpl<$Res>
    extends _$ShopStateCopyWithImpl<$Res, _$ShopStateImpl>
    implements _$$ShopStateImplCopyWith<$Res> {
  __$$ShopStateImplCopyWithImpl(
      _$ShopStateImpl _value, $Res Function(_$ShopStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ShopState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isFilterLoading = null,
    Object? isCategoryLoading = null,
    Object? isPopularLoading = null,
    Object? isProductLoading = null,
    Object? isProductCategoryLoading = null,
    Object? isPopularProduct = null,
    Object? isLike = null,
    Object? showWeekTime = null,
    Object? showBranch = null,
    Object? isMapLoading = null,
    Object? isGroupOrder = null,
    Object? isJoinOrder = null,
    Object? isSearchEnabled = null,
    Object? isTodayWorkingDay = null,
    Object? isTomorrowWorkingDay = null,
    Object? isNestedScrollDisabled = null,
    Object? userUuid = null,
    Object? searchText = null,
    Object? startTodayTime = null,
    Object? endTodayTime = null,
    Object? currentIndex = null,
    Object? subCategoryIndex = null,
    Object? shopMarkers = null,
    Object? polylineCoordinates = null,
    Object? shopData = freezed,
    Object? categoryProducts = null,
    Object? allData = null,
    Object? category = freezed,
    Object? brands = freezed,
    Object? branches = freezed,
    Object? brandIds = null,
    Object? sortIndex = null,
  }) {
    return _then(_$ShopStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isFilterLoading: null == isFilterLoading
          ? _value.isFilterLoading
          : isFilterLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCategoryLoading: null == isCategoryLoading
          ? _value.isCategoryLoading
          : isCategoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isPopularLoading: null == isPopularLoading
          ? _value.isPopularLoading
          : isPopularLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isProductLoading: null == isProductLoading
          ? _value.isProductLoading
          : isProductLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isProductCategoryLoading: null == isProductCategoryLoading
          ? _value.isProductCategoryLoading
          : isProductCategoryLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isPopularProduct: null == isPopularProduct
          ? _value.isPopularProduct
          : isPopularProduct // ignore: cast_nullable_to_non_nullable
              as bool,
      isLike: null == isLike
          ? _value.isLike
          : isLike // ignore: cast_nullable_to_non_nullable
              as bool,
      showWeekTime: null == showWeekTime
          ? _value.showWeekTime
          : showWeekTime // ignore: cast_nullable_to_non_nullable
              as bool,
      showBranch: null == showBranch
          ? _value.showBranch
          : showBranch // ignore: cast_nullable_to_non_nullable
              as bool,
      isMapLoading: null == isMapLoading
          ? _value.isMapLoading
          : isMapLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isGroupOrder: null == isGroupOrder
          ? _value.isGroupOrder
          : isGroupOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      isJoinOrder: null == isJoinOrder
          ? _value.isJoinOrder
          : isJoinOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      isSearchEnabled: null == isSearchEnabled
          ? _value.isSearchEnabled
          : isSearchEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      isTodayWorkingDay: null == isTodayWorkingDay
          ? _value.isTodayWorkingDay
          : isTodayWorkingDay // ignore: cast_nullable_to_non_nullable
              as bool,
      isTomorrowWorkingDay: null == isTomorrowWorkingDay
          ? _value.isTomorrowWorkingDay
          : isTomorrowWorkingDay // ignore: cast_nullable_to_non_nullable
              as bool,
      isNestedScrollDisabled: null == isNestedScrollDisabled
          ? _value.isNestedScrollDisabled
          : isNestedScrollDisabled // ignore: cast_nullable_to_non_nullable
              as bool,
      userUuid: null == userUuid
          ? _value.userUuid
          : userUuid // ignore: cast_nullable_to_non_nullable
              as String,
      searchText: null == searchText
          ? _value.searchText
          : searchText // ignore: cast_nullable_to_non_nullable
              as String,
      startTodayTime: null == startTodayTime
          ? _value.startTodayTime
          : startTodayTime // ignore: cast_nullable_to_non_nullable
              as TimeOfDay,
      endTodayTime: null == endTodayTime
          ? _value.endTodayTime
          : endTodayTime // ignore: cast_nullable_to_non_nullable
              as TimeOfDay,
      currentIndex: null == currentIndex
          ? _value.currentIndex
          : currentIndex // ignore: cast_nullable_to_non_nullable
              as int,
      subCategoryIndex: null == subCategoryIndex
          ? _value.subCategoryIndex
          : subCategoryIndex // ignore: cast_nullable_to_non_nullable
              as int,
      shopMarkers: null == shopMarkers
          ? _value._shopMarkers
          : shopMarkers // ignore: cast_nullable_to_non_nullable
              as Set<Marker>,
      polylineCoordinates: null == polylineCoordinates
          ? _value._polylineCoordinates
          : polylineCoordinates // ignore: cast_nullable_to_non_nullable
              as List<LatLng>,
      shopData: freezed == shopData
          ? _value.shopData
          : shopData // ignore: cast_nullable_to_non_nullable
              as ShopData?,
      categoryProducts: null == categoryProducts
          ? _value._categoryProducts
          : categoryProducts // ignore: cast_nullable_to_non_nullable
              as List<ProductData>,
      allData: null == allData
          ? _value._allData
          : allData // ignore: cast_nullable_to_non_nullable
              as List<All>,
      category: freezed == category
          ? _value._category
          : category // ignore: cast_nullable_to_non_nullable
              as List<CategoryData>?,
      brands: freezed == brands
          ? _value._brands
          : brands // ignore: cast_nullable_to_non_nullable
              as List<BrandData>?,
      branches: freezed == branches
          ? _value._branches
          : branches // ignore: cast_nullable_to_non_nullable
              as List<BranchModel>?,
      brandIds: null == brandIds
          ? _value._brandIds
          : brandIds // ignore: cast_nullable_to_non_nullable
              as List<int>,
      sortIndex: null == sortIndex
          ? _value.sortIndex
          : sortIndex // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ShopStateImpl extends _ShopState {
  const _$ShopStateImpl(
      {this.isLoading = false,
      this.isFilterLoading = false,
      this.isCategoryLoading = true,
      this.isPopularLoading = true,
      this.isProductLoading = true,
      this.isProductCategoryLoading = false,
      this.isPopularProduct = false,
      this.isLike = false,
      this.showWeekTime = false,
      this.showBranch = false,
      this.isMapLoading = false,
      this.isGroupOrder = false,
      this.isJoinOrder = false,
      this.isSearchEnabled = false,
      this.isTodayWorkingDay = false,
      this.isTomorrowWorkingDay = false,
      this.isNestedScrollDisabled = false,
      this.userUuid = "",
      this.searchText = "",
      this.startTodayTime = const TimeOfDay(hour: 0, minute: 0),
      this.endTodayTime = const TimeOfDay(hour: 0, minute: 0),
      this.currentIndex = 0,
      this.subCategoryIndex = 0,
      final Set<Marker> shopMarkers = const {},
      final List<LatLng> polylineCoordinates = const [],
      this.shopData = null,
      final List<ProductData> categoryProducts = const [],
      final List<All> allData = const [],
      final List<CategoryData>? category = const [],
      final List<BrandData>? brands = const [],
      final List<BranchModel>? branches = const [],
      final List<int> brandIds = const [],
      this.sortIndex = 0})
      : _shopMarkers = shopMarkers,
        _polylineCoordinates = polylineCoordinates,
        _categoryProducts = categoryProducts,
        _allData = allData,
        _category = category,
        _brands = brands,
        _branches = branches,
        _brandIds = brandIds,
        super._();

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isFilterLoading;
  @override
  @JsonKey()
  final bool isCategoryLoading;
  @override
  @JsonKey()
  final bool isPopularLoading;
  @override
  @JsonKey()
  final bool isProductLoading;
  @override
  @JsonKey()
  final bool isProductCategoryLoading;
  @override
  @JsonKey()
  final bool isPopularProduct;
  @override
  @JsonKey()
  final bool isLike;
  @override
  @JsonKey()
  final bool showWeekTime;
  @override
  @JsonKey()
  final bool showBranch;
  @override
  @JsonKey()
  final bool isMapLoading;
  @override
  @JsonKey()
  final bool isGroupOrder;
  @override
  @JsonKey()
  final bool isJoinOrder;
  @override
  @JsonKey()
  final bool isSearchEnabled;
  @override
  @JsonKey()
  final bool isTodayWorkingDay;
  @override
  @JsonKey()
  final bool isTomorrowWorkingDay;
  @override
  @JsonKey()
  final bool isNestedScrollDisabled;
  @override
  @JsonKey()
  final String userUuid;
  @override
  @JsonKey()
  final String searchText;
  @override
  @JsonKey()
  final TimeOfDay startTodayTime;
  @override
  @JsonKey()
  final TimeOfDay endTodayTime;
  @override
  @JsonKey()
  final int currentIndex;
  @override
  @JsonKey()
  final int subCategoryIndex;
  final Set<Marker> _shopMarkers;
  @override
  @JsonKey()
  Set<Marker> get shopMarkers {
    if (_shopMarkers is EqualUnmodifiableSetView) return _shopMarkers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_shopMarkers);
  }

  final List<LatLng> _polylineCoordinates;
  @override
  @JsonKey()
  List<LatLng> get polylineCoordinates {
    if (_polylineCoordinates is EqualUnmodifiableListView)
      return _polylineCoordinates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_polylineCoordinates);
  }

  @override
  @JsonKey()
  final ShopData? shopData;
// @Default([]) List<ProductData> products,
// @Default([]) List<ProductData> popularProducts,
  final List<ProductData> _categoryProducts;
// @Default([]) List<ProductData> products,
// @Default([]) List<ProductData> popularProducts,
  @override
  @JsonKey()
  List<ProductData> get categoryProducts {
    if (_categoryProducts is EqualUnmodifiableListView)
      return _categoryProducts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_categoryProducts);
  }

  final List<All> _allData;
  @override
  @JsonKey()
  List<All> get allData {
    if (_allData is EqualUnmodifiableListView) return _allData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allData);
  }

  final List<CategoryData>? _category;
  @override
  @JsonKey()
  List<CategoryData>? get category {
    final value = _category;
    if (value == null) return null;
    if (_category is EqualUnmodifiableListView) return _category;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<BrandData>? _brands;
  @override
  @JsonKey()
  List<BrandData>? get brands {
    final value = _brands;
    if (value == null) return null;
    if (_brands is EqualUnmodifiableListView) return _brands;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<BranchModel>? _branches;
  @override
  @JsonKey()
  List<BranchModel>? get branches {
    final value = _branches;
    if (value == null) return null;
    if (_branches is EqualUnmodifiableListView) return _branches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<int> _brandIds;
  @override
  @JsonKey()
  List<int> get brandIds {
    if (_brandIds is EqualUnmodifiableListView) return _brandIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_brandIds);
  }

  @override
  @JsonKey()
  final int sortIndex;

  @override
  String toString() {
    return 'ShopState(isLoading: $isLoading, isFilterLoading: $isFilterLoading, isCategoryLoading: $isCategoryLoading, isPopularLoading: $isPopularLoading, isProductLoading: $isProductLoading, isProductCategoryLoading: $isProductCategoryLoading, isPopularProduct: $isPopularProduct, isLike: $isLike, showWeekTime: $showWeekTime, showBranch: $showBranch, isMapLoading: $isMapLoading, isGroupOrder: $isGroupOrder, isJoinOrder: $isJoinOrder, isSearchEnabled: $isSearchEnabled, isTodayWorkingDay: $isTodayWorkingDay, isTomorrowWorkingDay: $isTomorrowWorkingDay, isNestedScrollDisabled: $isNestedScrollDisabled, userUuid: $userUuid, searchText: $searchText, startTodayTime: $startTodayTime, endTodayTime: $endTodayTime, currentIndex: $currentIndex, subCategoryIndex: $subCategoryIndex, shopMarkers: $shopMarkers, polylineCoordinates: $polylineCoordinates, shopData: $shopData, categoryProducts: $categoryProducts, allData: $allData, category: $category, brands: $brands, branches: $branches, brandIds: $brandIds, sortIndex: $sortIndex)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShopStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isFilterLoading, isFilterLoading) ||
                other.isFilterLoading == isFilterLoading) &&
            (identical(other.isCategoryLoading, isCategoryLoading) ||
                other.isCategoryLoading == isCategoryLoading) &&
            (identical(other.isPopularLoading, isPopularLoading) ||
                other.isPopularLoading == isPopularLoading) &&
            (identical(other.isProductLoading, isProductLoading) ||
                other.isProductLoading == isProductLoading) &&
            (identical(
                    other.isProductCategoryLoading, isProductCategoryLoading) ||
                other.isProductCategoryLoading == isProductCategoryLoading) &&
            (identical(other.isPopularProduct, isPopularProduct) ||
                other.isPopularProduct == isPopularProduct) &&
            (identical(other.isLike, isLike) || other.isLike == isLike) &&
            (identical(other.showWeekTime, showWeekTime) ||
                other.showWeekTime == showWeekTime) &&
            (identical(other.showBranch, showBranch) ||
                other.showBranch == showBranch) &&
            (identical(other.isMapLoading, isMapLoading) ||
                other.isMapLoading == isMapLoading) &&
            (identical(other.isGroupOrder, isGroupOrder) ||
                other.isGroupOrder == isGroupOrder) &&
            (identical(other.isJoinOrder, isJoinOrder) ||
                other.isJoinOrder == isJoinOrder) &&
            (identical(other.isSearchEnabled, isSearchEnabled) ||
                other.isSearchEnabled == isSearchEnabled) &&
            (identical(other.isTodayWorkingDay, isTodayWorkingDay) ||
                other.isTodayWorkingDay == isTodayWorkingDay) &&
            (identical(other.isTomorrowWorkingDay, isTomorrowWorkingDay) ||
                other.isTomorrowWorkingDay == isTomorrowWorkingDay) &&
            (identical(other.isNestedScrollDisabled, isNestedScrollDisabled) ||
                other.isNestedScrollDisabled == isNestedScrollDisabled) &&
            (identical(other.userUuid, userUuid) ||
                other.userUuid == userUuid) &&
            (identical(other.searchText, searchText) ||
                other.searchText == searchText) &&
            (identical(other.startTodayTime, startTodayTime) ||
                other.startTodayTime == startTodayTime) &&
            (identical(other.endTodayTime, endTodayTime) ||
                other.endTodayTime == endTodayTime) &&
            (identical(other.currentIndex, currentIndex) ||
                other.currentIndex == currentIndex) &&
            (identical(other.subCategoryIndex, subCategoryIndex) ||
                other.subCategoryIndex == subCategoryIndex) &&
            const DeepCollectionEquality()
                .equals(other._shopMarkers, _shopMarkers) &&
            const DeepCollectionEquality()
                .equals(other._polylineCoordinates, _polylineCoordinates) &&
            (identical(other.shopData, shopData) ||
                other.shopData == shopData) &&
            const DeepCollectionEquality()
                .equals(other._categoryProducts, _categoryProducts) &&
            const DeepCollectionEquality().equals(other._allData, _allData) &&
            const DeepCollectionEquality().equals(other._category, _category) &&
            const DeepCollectionEquality().equals(other._brands, _brands) &&
            const DeepCollectionEquality().equals(other._branches, _branches) &&
            const DeepCollectionEquality().equals(other._brandIds, _brandIds) &&
            (identical(other.sortIndex, sortIndex) ||
                other.sortIndex == sortIndex));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        isLoading,
        isFilterLoading,
        isCategoryLoading,
        isPopularLoading,
        isProductLoading,
        isProductCategoryLoading,
        isPopularProduct,
        isLike,
        showWeekTime,
        showBranch,
        isMapLoading,
        isGroupOrder,
        isJoinOrder,
        isSearchEnabled,
        isTodayWorkingDay,
        isTomorrowWorkingDay,
        isNestedScrollDisabled,
        userUuid,
        searchText,
        startTodayTime,
        endTodayTime,
        currentIndex,
        subCategoryIndex,
        const DeepCollectionEquality().hash(_shopMarkers),
        const DeepCollectionEquality().hash(_polylineCoordinates),
        shopData,
        const DeepCollectionEquality().hash(_categoryProducts),
        const DeepCollectionEquality().hash(_allData),
        const DeepCollectionEquality().hash(_category),
        const DeepCollectionEquality().hash(_brands),
        const DeepCollectionEquality().hash(_branches),
        const DeepCollectionEquality().hash(_brandIds),
        sortIndex
      ]);

  /// Create a copy of ShopState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShopStateImplCopyWith<_$ShopStateImpl> get copyWith =>
      __$$ShopStateImplCopyWithImpl<_$ShopStateImpl>(this, _$identity);
}

abstract class _ShopState extends ShopState {
  const factory _ShopState(
      {final bool isLoading,
      final bool isFilterLoading,
      final bool isCategoryLoading,
      final bool isPopularLoading,
      final bool isProductLoading,
      final bool isProductCategoryLoading,
      final bool isPopularProduct,
      final bool isLike,
      final bool showWeekTime,
      final bool showBranch,
      final bool isMapLoading,
      final bool isGroupOrder,
      final bool isJoinOrder,
      final bool isSearchEnabled,
      final bool isTodayWorkingDay,
      final bool isTomorrowWorkingDay,
      final bool isNestedScrollDisabled,
      final String userUuid,
      final String searchText,
      final TimeOfDay startTodayTime,
      final TimeOfDay endTodayTime,
      final int currentIndex,
      final int subCategoryIndex,
      final Set<Marker> shopMarkers,
      final List<LatLng> polylineCoordinates,
      final ShopData? shopData,
      final List<ProductData> categoryProducts,
      final List<All> allData,
      final List<CategoryData>? category,
      final List<BrandData>? brands,
      final List<BranchModel>? branches,
      final List<int> brandIds,
      final int sortIndex}) = _$ShopStateImpl;
  const _ShopState._() : super._();

  @override
  bool get isLoading;
  @override
  bool get isFilterLoading;
  @override
  bool get isCategoryLoading;
  @override
  bool get isPopularLoading;
  @override
  bool get isProductLoading;
  @override
  bool get isProductCategoryLoading;
  @override
  bool get isPopularProduct;
  @override
  bool get isLike;
  @override
  bool get showWeekTime;
  @override
  bool get showBranch;
  @override
  bool get isMapLoading;
  @override
  bool get isGroupOrder;
  @override
  bool get isJoinOrder;
  @override
  bool get isSearchEnabled;
  @override
  bool get isTodayWorkingDay;
  @override
  bool get isTomorrowWorkingDay;
  @override
  bool get isNestedScrollDisabled;
  @override
  String get userUuid;
  @override
  String get searchText;
  @override
  TimeOfDay get startTodayTime;
  @override
  TimeOfDay get endTodayTime;
  @override
  int get currentIndex;
  @override
  int get subCategoryIndex;
  @override
  Set<Marker> get shopMarkers;
  @override
  List<LatLng> get polylineCoordinates;
  @override
  ShopData? get shopData; // @Default([]) List<ProductData> products,
// @Default([]) List<ProductData> popularProducts,
  @override
  List<ProductData> get categoryProducts;
  @override
  List<All> get allData;
  @override
  List<CategoryData>? get category;
  @override
  List<BrandData>? get brands;
  @override
  List<BranchModel>? get branches;
  @override
  List<int> get brandIds;
  @override
  int get sortIndex;

  /// Create a copy of ShopState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShopStateImplCopyWith<_$ShopStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
