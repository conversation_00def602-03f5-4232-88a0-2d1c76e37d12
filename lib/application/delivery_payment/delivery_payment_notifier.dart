import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:pandoo_delivery/infrastructure/models/data/delivery_payment_method.dart';
import 'package:pandoo_delivery/infrastructure/services/app_connectivity.dart';
import 'package:pandoo_delivery/infrastructure/services/app_helpers.dart';
import 'package:pandoo_delivery/infrastructure/services/brazilian_payment_methods.dart';
import 'package:pandoo_delivery/domain/interface/shops.dart';
import 'delivery_payment_state.dart';

class DeliveryPaymentNotifier extends StateNotifier<DeliveryPaymentState> {
  final ShopsRepositoryFacade _shopsRepository;

  DeliveryPaymentNotifier(this._shopsRepository) : super(const DeliveryPaymentState());

  void selectPaymentMethod(int index) {
    state = state.copyWith(selectedIndex: index);
  }

  void setChangeRequired(bool required) {
    state = state.copyWith(
      changeRequired: required,
      changeAmount: required ? state.changeAmount : 0.0,
      cashPaymentAmount: required ? state.cashPaymentAmount : 0.0,
    );
  }

  void setChangeAmount(double amount) {
    state = state.copyWith(changeAmount: amount);
  }

  void setCashPaymentAmount(double amount) {
    state = state.copyWith(cashPaymentAmount: amount);
  }

  void setOrderTotal(double total) {
    state = state.copyWith(orderTotal: total);
  }

  void setSelectedMethodForUI(DeliveryPaymentMethod method) {
    state = state.copyWith(selectedMethodForUI: method);
  }

  void clearSelectedMethodForUI() {
    state = state.copyWith(selectedMethodForUI: null);
  }

  void loadDefaultMethods() {
    final defaultMethods = BrazilianPaymentMethods.getDefaultMethods();
    state = state.copyWith(
      methods: defaultMethods,
      instructions: 'Escolha como você gostaria de pagar na entrega',
      isLoading: false,
      error: '',
    );
  }

  Future<void> fetchDeliveryPaymentMethods(
    BuildContext context,
    int shopId,
  ) async {
    final connected = await AppConnectivity.connectivity();
    if (connected) {
      state = state.copyWith(isLoading: true);
      final response = await _shopsRepository.getDeliveryPaymentMethods(shopId);
      response.when(
        success: (data) {
          // If API returns empty list, use Brazilian defaults
          if (data.deliveryPaymentMethods.isEmpty) {
            final defaultMethods = BrazilianPaymentMethods.getDefaultMethods();
            state = state.copyWith(
              methods: defaultMethods,
              instructions: 'Escolha como você gostaria de pagar na entrega',
              isLoading: false,
              error: '',
            );
          } else {
            state = state.copyWith(
              methods: data.deliveryPaymentMethods,
              instructions: data.instructions,
              isLoading: false,
              error: '',
            );
          }
        },
        failure: (failure, status) {
          // Load default methods as fallback
          final defaultMethods = BrazilianPaymentMethods.getDefaultMethods();
          state = state.copyWith(
            methods: defaultMethods,
            instructions: 'Escolha como você gostaria de pagar na entrega',
            isLoading: false,
            error: '',
          );
        },
      );
    } else {
      // Load default methods when no connectivity
      final defaultMethods = BrazilianPaymentMethods.getDefaultMethods();
      state = state.copyWith(
        methods: defaultMethods,
        instructions: 'Escolha como você gostaria de pagar na entrega',
        isLoading: false,
        error: '',
      );

      if (context.mounted) {
        AppHelpers.showNoConnectionSnackBar(context);
      }
    }
  }

  void reset() {
    state = const DeliveryPaymentState();
  }
}
