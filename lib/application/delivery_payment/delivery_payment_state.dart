import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:pandoo_delivery/infrastructure/models/data/delivery_payment_method.dart';

part 'delivery_payment_state.freezed.dart';

@freezed
class DeliveryPaymentState with _$DeliveryPaymentState {
  const factory DeliveryPaymentState({
    @Default(false) bool isLoading,
    @Default([]) List<DeliveryPaymentMethod> methods,
    @Default('') String instructions,
    @Default('') String error,
    @Default(-1) int selectedIndex,
    @Default(false) bool changeRequired,
    @Default(0.0) double changeAmount,
    @Default(0.0) double cashPaymentAmount,
    @Default(0.0) double orderTotal,
    @Default(null) DeliveryPaymentMethod? selectedMethodForUI,
  }) = _DeliveryPaymentState;

  const DeliveryPaymentState._();

  /// Calculate the change amount automatically
  double get calculatedChange {
    if (!changeRequired || cashPaymentAmount <= 0 || orderTotal <= 0) {
      return 0.0;
    }
    return cashPaymentAmount - orderTotal;
  }

  /// Check if the cash payment amount is valid
  bool get isValidCashAmount {
    if (!changeRequired) return true;
    return cashPaymentAmount >= orderTotal;
  }
}
