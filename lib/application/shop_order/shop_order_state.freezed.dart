// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shop_order_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ShopOrderState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isStartGroupLoading => throw _privateConstructorUsedError;
  bool get isStartGroup => throw _privateConstructorUsedError;
  bool get isOtherShop => throw _privateConstructorUsedError;
  bool get isDeleteLoading => throw _privateConstructorUsedError;
  bool get isCheckShopOrder => throw _privateConstructorUsedError;
  bool get isAddAndRemoveLoading => throw _privateConstructorUsedError;
  bool get isEditOrder => throw _privateConstructorUsedError;
  String get shareLink => throw _privateConstructorUsedError;
  Cart? get cart => throw _privateConstructorUsedError;
  List<CartProductData> get productList => throw _privateConstructorUsedError;

  /// Create a copy of ShopOrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShopOrderStateCopyWith<ShopOrderState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShopOrderStateCopyWith<$Res> {
  factory $ShopOrderStateCopyWith(
          ShopOrderState value, $Res Function(ShopOrderState) then) =
      _$ShopOrderStateCopyWithImpl<$Res, ShopOrderState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isStartGroupLoading,
      bool isStartGroup,
      bool isOtherShop,
      bool isDeleteLoading,
      bool isCheckShopOrder,
      bool isAddAndRemoveLoading,
      bool isEditOrder,
      String shareLink,
      Cart? cart,
      List<CartProductData> productList});
}

/// @nodoc
class _$ShopOrderStateCopyWithImpl<$Res, $Val extends ShopOrderState>
    implements $ShopOrderStateCopyWith<$Res> {
  _$ShopOrderStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShopOrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isStartGroupLoading = null,
    Object? isStartGroup = null,
    Object? isOtherShop = null,
    Object? isDeleteLoading = null,
    Object? isCheckShopOrder = null,
    Object? isAddAndRemoveLoading = null,
    Object? isEditOrder = null,
    Object? shareLink = null,
    Object? cart = freezed,
    Object? productList = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isStartGroupLoading: null == isStartGroupLoading
          ? _value.isStartGroupLoading
          : isStartGroupLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isStartGroup: null == isStartGroup
          ? _value.isStartGroup
          : isStartGroup // ignore: cast_nullable_to_non_nullable
              as bool,
      isOtherShop: null == isOtherShop
          ? _value.isOtherShop
          : isOtherShop // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeleteLoading: null == isDeleteLoading
          ? _value.isDeleteLoading
          : isDeleteLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckShopOrder: null == isCheckShopOrder
          ? _value.isCheckShopOrder
          : isCheckShopOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      isAddAndRemoveLoading: null == isAddAndRemoveLoading
          ? _value.isAddAndRemoveLoading
          : isAddAndRemoveLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEditOrder: null == isEditOrder
          ? _value.isEditOrder
          : isEditOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      shareLink: null == shareLink
          ? _value.shareLink
          : shareLink // ignore: cast_nullable_to_non_nullable
              as String,
      cart: freezed == cart
          ? _value.cart
          : cart // ignore: cast_nullable_to_non_nullable
              as Cart?,
      productList: null == productList
          ? _value.productList
          : productList // ignore: cast_nullable_to_non_nullable
              as List<CartProductData>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShopOrderStateImplCopyWith<$Res>
    implements $ShopOrderStateCopyWith<$Res> {
  factory _$$ShopOrderStateImplCopyWith(_$ShopOrderStateImpl value,
          $Res Function(_$ShopOrderStateImpl) then) =
      __$$ShopOrderStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isStartGroupLoading,
      bool isStartGroup,
      bool isOtherShop,
      bool isDeleteLoading,
      bool isCheckShopOrder,
      bool isAddAndRemoveLoading,
      bool isEditOrder,
      String shareLink,
      Cart? cart,
      List<CartProductData> productList});
}

/// @nodoc
class __$$ShopOrderStateImplCopyWithImpl<$Res>
    extends _$ShopOrderStateCopyWithImpl<$Res, _$ShopOrderStateImpl>
    implements _$$ShopOrderStateImplCopyWith<$Res> {
  __$$ShopOrderStateImplCopyWithImpl(
      _$ShopOrderStateImpl _value, $Res Function(_$ShopOrderStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ShopOrderState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isStartGroupLoading = null,
    Object? isStartGroup = null,
    Object? isOtherShop = null,
    Object? isDeleteLoading = null,
    Object? isCheckShopOrder = null,
    Object? isAddAndRemoveLoading = null,
    Object? isEditOrder = null,
    Object? shareLink = null,
    Object? cart = freezed,
    Object? productList = null,
  }) {
    return _then(_$ShopOrderStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isStartGroupLoading: null == isStartGroupLoading
          ? _value.isStartGroupLoading
          : isStartGroupLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isStartGroup: null == isStartGroup
          ? _value.isStartGroup
          : isStartGroup // ignore: cast_nullable_to_non_nullable
              as bool,
      isOtherShop: null == isOtherShop
          ? _value.isOtherShop
          : isOtherShop // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeleteLoading: null == isDeleteLoading
          ? _value.isDeleteLoading
          : isDeleteLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckShopOrder: null == isCheckShopOrder
          ? _value.isCheckShopOrder
          : isCheckShopOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      isAddAndRemoveLoading: null == isAddAndRemoveLoading
          ? _value.isAddAndRemoveLoading
          : isAddAndRemoveLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isEditOrder: null == isEditOrder
          ? _value.isEditOrder
          : isEditOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      shareLink: null == shareLink
          ? _value.shareLink
          : shareLink // ignore: cast_nullable_to_non_nullable
              as String,
      cart: freezed == cart
          ? _value.cart
          : cart // ignore: cast_nullable_to_non_nullable
              as Cart?,
      productList: null == productList
          ? _value._productList
          : productList // ignore: cast_nullable_to_non_nullable
              as List<CartProductData>,
    ));
  }
}

/// @nodoc

class _$ShopOrderStateImpl extends _ShopOrderState {
  const _$ShopOrderStateImpl(
      {this.isLoading = false,
      this.isStartGroupLoading = false,
      this.isStartGroup = false,
      this.isOtherShop = false,
      this.isDeleteLoading = false,
      this.isCheckShopOrder = false,
      this.isAddAndRemoveLoading = false,
      this.isEditOrder = false,
      this.shareLink = "",
      this.cart = null,
      final List<CartProductData> productList = const []})
      : _productList = productList,
        super._();

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isStartGroupLoading;
  @override
  @JsonKey()
  final bool isStartGroup;
  @override
  @JsonKey()
  final bool isOtherShop;
  @override
  @JsonKey()
  final bool isDeleteLoading;
  @override
  @JsonKey()
  final bool isCheckShopOrder;
  @override
  @JsonKey()
  final bool isAddAndRemoveLoading;
  @override
  @JsonKey()
  final bool isEditOrder;
  @override
  @JsonKey()
  final String shareLink;
  @override
  @JsonKey()
  final Cart? cart;
  final List<CartProductData> _productList;
  @override
  @JsonKey()
  List<CartProductData> get productList {
    if (_productList is EqualUnmodifiableListView) return _productList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_productList);
  }

  @override
  String toString() {
    return 'ShopOrderState(isLoading: $isLoading, isStartGroupLoading: $isStartGroupLoading, isStartGroup: $isStartGroup, isOtherShop: $isOtherShop, isDeleteLoading: $isDeleteLoading, isCheckShopOrder: $isCheckShopOrder, isAddAndRemoveLoading: $isAddAndRemoveLoading, isEditOrder: $isEditOrder, shareLink: $shareLink, cart: $cart, productList: $productList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShopOrderStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isStartGroupLoading, isStartGroupLoading) ||
                other.isStartGroupLoading == isStartGroupLoading) &&
            (identical(other.isStartGroup, isStartGroup) ||
                other.isStartGroup == isStartGroup) &&
            (identical(other.isOtherShop, isOtherShop) ||
                other.isOtherShop == isOtherShop) &&
            (identical(other.isDeleteLoading, isDeleteLoading) ||
                other.isDeleteLoading == isDeleteLoading) &&
            (identical(other.isCheckShopOrder, isCheckShopOrder) ||
                other.isCheckShopOrder == isCheckShopOrder) &&
            (identical(other.isAddAndRemoveLoading, isAddAndRemoveLoading) ||
                other.isAddAndRemoveLoading == isAddAndRemoveLoading) &&
            (identical(other.isEditOrder, isEditOrder) ||
                other.isEditOrder == isEditOrder) &&
            (identical(other.shareLink, shareLink) ||
                other.shareLink == shareLink) &&
            (identical(other.cart, cart) || other.cart == cart) &&
            const DeepCollectionEquality()
                .equals(other._productList, _productList));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isStartGroupLoading,
      isStartGroup,
      isOtherShop,
      isDeleteLoading,
      isCheckShopOrder,
      isAddAndRemoveLoading,
      isEditOrder,
      shareLink,
      cart,
      const DeepCollectionEquality().hash(_productList));

  /// Create a copy of ShopOrderState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShopOrderStateImplCopyWith<_$ShopOrderStateImpl> get copyWith =>
      __$$ShopOrderStateImplCopyWithImpl<_$ShopOrderStateImpl>(
          this, _$identity);
}

abstract class _ShopOrderState extends ShopOrderState {
  const factory _ShopOrderState(
      {final bool isLoading,
      final bool isStartGroupLoading,
      final bool isStartGroup,
      final bool isOtherShop,
      final bool isDeleteLoading,
      final bool isCheckShopOrder,
      final bool isAddAndRemoveLoading,
      final bool isEditOrder,
      final String shareLink,
      final Cart? cart,
      final List<CartProductData> productList}) = _$ShopOrderStateImpl;
  const _ShopOrderState._() : super._();

  @override
  bool get isLoading;
  @override
  bool get isStartGroupLoading;
  @override
  bool get isStartGroup;
  @override
  bool get isOtherShop;
  @override
  bool get isDeleteLoading;
  @override
  bool get isCheckShopOrder;
  @override
  bool get isAddAndRemoveLoading;
  @override
  bool get isEditOrder;
  @override
  String get shareLink;
  @override
  Cart? get cart;
  @override
  List<CartProductData> get productList;

  /// Create a copy of ShopOrderState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShopOrderStateImplCopyWith<_$ShopOrderStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
