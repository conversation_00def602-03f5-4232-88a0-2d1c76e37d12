// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reset_password_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ResetPasswordState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isSuccess => throw _privateConstructorUsedError;
  bool get isEmailError => throw _privateConstructorUsedError;
  bool get isPasswordInvalid => throw _privateConstructorUsedError;
  bool get isConfirmPasswordInvalid => throw _privateConstructorUsedError;
  bool get showPassword => throw _privateConstructorUsedError;
  bool get showConfirmPassword => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String get verifyId => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  String get confirmPassword => throw _privateConstructorUsedError;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ResetPasswordStateCopyWith<ResetPasswordState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResetPasswordStateCopyWith<$Res> {
  factory $ResetPasswordStateCopyWith(
          ResetPasswordState value, $Res Function(ResetPasswordState) then) =
      _$ResetPasswordStateCopyWithImpl<$Res, ResetPasswordState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      bool isEmailError,
      bool isPasswordInvalid,
      bool isConfirmPasswordInvalid,
      bool showPassword,
      bool showConfirmPassword,
      String email,
      String phone,
      String verifyId,
      String password,
      String confirmPassword});
}

/// @nodoc
class _$ResetPasswordStateCopyWithImpl<$Res, $Val extends ResetPasswordState>
    implements $ResetPasswordStateCopyWith<$Res> {
  _$ResetPasswordStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? isEmailError = null,
    Object? isPasswordInvalid = null,
    Object? isConfirmPasswordInvalid = null,
    Object? showPassword = null,
    Object? showConfirmPassword = null,
    Object? email = null,
    Object? phone = null,
    Object? verifyId = null,
    Object? password = null,
    Object? confirmPassword = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailError: null == isEmailError
          ? _value.isEmailError
          : isEmailError // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordInvalid: null == isPasswordInvalid
          ? _value.isPasswordInvalid
          : isPasswordInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirmPasswordInvalid: null == isConfirmPasswordInvalid
          ? _value.isConfirmPasswordInvalid
          : isConfirmPasswordInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      showPassword: null == showPassword
          ? _value.showPassword
          : showPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      showConfirmPassword: null == showConfirmPassword
          ? _value.showConfirmPassword
          : showConfirmPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      verifyId: null == verifyId
          ? _value.verifyId
          : verifyId // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      confirmPassword: null == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ResetPasswordStateImplCopyWith<$Res>
    implements $ResetPasswordStateCopyWith<$Res> {
  factory _$$ResetPasswordStateImplCopyWith(_$ResetPasswordStateImpl value,
          $Res Function(_$ResetPasswordStateImpl) then) =
      __$$ResetPasswordStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      bool isEmailError,
      bool isPasswordInvalid,
      bool isConfirmPasswordInvalid,
      bool showPassword,
      bool showConfirmPassword,
      String email,
      String phone,
      String verifyId,
      String password,
      String confirmPassword});
}

/// @nodoc
class __$$ResetPasswordStateImplCopyWithImpl<$Res>
    extends _$ResetPasswordStateCopyWithImpl<$Res, _$ResetPasswordStateImpl>
    implements _$$ResetPasswordStateImplCopyWith<$Res> {
  __$$ResetPasswordStateImplCopyWithImpl(_$ResetPasswordStateImpl _value,
      $Res Function(_$ResetPasswordStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? isEmailError = null,
    Object? isPasswordInvalid = null,
    Object? isConfirmPasswordInvalid = null,
    Object? showPassword = null,
    Object? showConfirmPassword = null,
    Object? email = null,
    Object? phone = null,
    Object? verifyId = null,
    Object? password = null,
    Object? confirmPassword = null,
  }) {
    return _then(_$ResetPasswordStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailError: null == isEmailError
          ? _value.isEmailError
          : isEmailError // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordInvalid: null == isPasswordInvalid
          ? _value.isPasswordInvalid
          : isPasswordInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirmPasswordInvalid: null == isConfirmPasswordInvalid
          ? _value.isConfirmPasswordInvalid
          : isConfirmPasswordInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      showPassword: null == showPassword
          ? _value.showPassword
          : showPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      showConfirmPassword: null == showConfirmPassword
          ? _value.showConfirmPassword
          : showConfirmPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      verifyId: null == verifyId
          ? _value.verifyId
          : verifyId // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      confirmPassword: null == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ResetPasswordStateImpl extends _ResetPasswordState {
  const _$ResetPasswordStateImpl(
      {this.isLoading = false,
      this.isSuccess = false,
      this.isEmailError = false,
      this.isPasswordInvalid = false,
      this.isConfirmPasswordInvalid = false,
      this.showPassword = false,
      this.showConfirmPassword = false,
      this.email = '',
      this.phone = '',
      this.verifyId = '',
      this.password = '',
      this.confirmPassword = ''})
      : super._();

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSuccess;
  @override
  @JsonKey()
  final bool isEmailError;
  @override
  @JsonKey()
  final bool isPasswordInvalid;
  @override
  @JsonKey()
  final bool isConfirmPasswordInvalid;
  @override
  @JsonKey()
  final bool showPassword;
  @override
  @JsonKey()
  final bool showConfirmPassword;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final String phone;
  @override
  @JsonKey()
  final String verifyId;
  @override
  @JsonKey()
  final String password;
  @override
  @JsonKey()
  final String confirmPassword;

  @override
  String toString() {
    return 'ResetPasswordState(isLoading: $isLoading, isSuccess: $isSuccess, isEmailError: $isEmailError, isPasswordInvalid: $isPasswordInvalid, isConfirmPasswordInvalid: $isConfirmPasswordInvalid, showPassword: $showPassword, showConfirmPassword: $showConfirmPassword, email: $email, phone: $phone, verifyId: $verifyId, password: $password, confirmPassword: $confirmPassword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResetPasswordStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSuccess, isSuccess) ||
                other.isSuccess == isSuccess) &&
            (identical(other.isEmailError, isEmailError) ||
                other.isEmailError == isEmailError) &&
            (identical(other.isPasswordInvalid, isPasswordInvalid) ||
                other.isPasswordInvalid == isPasswordInvalid) &&
            (identical(
                    other.isConfirmPasswordInvalid, isConfirmPasswordInvalid) ||
                other.isConfirmPasswordInvalid == isConfirmPasswordInvalid) &&
            (identical(other.showPassword, showPassword) ||
                other.showPassword == showPassword) &&
            (identical(other.showConfirmPassword, showConfirmPassword) ||
                other.showConfirmPassword == showConfirmPassword) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.verifyId, verifyId) ||
                other.verifyId == verifyId) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.confirmPassword, confirmPassword) ||
                other.confirmPassword == confirmPassword));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isSuccess,
      isEmailError,
      isPasswordInvalid,
      isConfirmPasswordInvalid,
      showPassword,
      showConfirmPassword,
      email,
      phone,
      verifyId,
      password,
      confirmPassword);

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResetPasswordStateImplCopyWith<_$ResetPasswordStateImpl> get copyWith =>
      __$$ResetPasswordStateImplCopyWithImpl<_$ResetPasswordStateImpl>(
          this, _$identity);
}

abstract class _ResetPasswordState extends ResetPasswordState {
  const factory _ResetPasswordState(
      {final bool isLoading,
      final bool isSuccess,
      final bool isEmailError,
      final bool isPasswordInvalid,
      final bool isConfirmPasswordInvalid,
      final bool showPassword,
      final bool showConfirmPassword,
      final String email,
      final String phone,
      final String verifyId,
      final String password,
      final String confirmPassword}) = _$ResetPasswordStateImpl;
  const _ResetPasswordState._() : super._();

  @override
  bool get isLoading;
  @override
  bool get isSuccess;
  @override
  bool get isEmailError;
  @override
  bool get isPasswordInvalid;
  @override
  bool get isConfirmPasswordInvalid;
  @override
  bool get showPassword;
  @override
  bool get showConfirmPassword;
  @override
  String get email;
  @override
  String get phone;
  @override
  String get verifyId;
  @override
  String get password;
  @override
  String get confirmPassword;

  /// Create a copy of ResetPasswordState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResetPasswordStateImplCopyWith<_$ResetPasswordStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
