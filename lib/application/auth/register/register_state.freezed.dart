// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'register_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RegisterState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isSuccess => throw _privateConstructorUsedError;
  bool get showPassword => throw _privateConstructorUsedError;
  bool get showConfirmPassword => throw _privateConstructorUsedError;
  bool get isEmailInvalid => throw _privateConstructorUsedError;
  bool get isPasswordInvalid => throw _privateConstructorUsedError;
  bool get isConfirmPasswordInvalid => throw _privateConstructorUsedError;
  String get phone => throw _privateConstructorUsedError;
  String get verificationId => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get referral => throw _privateConstructorUsedError;
  String get firstName => throw _privateConstructorUsedError;
  String get lastName => throw _privateConstructorUsedError;
  String get password => throw _privateConstructorUsedError;
  String get confirmPassword => throw _privateConstructorUsedError;

  /// Create a copy of RegisterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RegisterStateCopyWith<RegisterState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RegisterStateCopyWith<$Res> {
  factory $RegisterStateCopyWith(
          RegisterState value, $Res Function(RegisterState) then) =
      _$RegisterStateCopyWithImpl<$Res, RegisterState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      bool showPassword,
      bool showConfirmPassword,
      bool isEmailInvalid,
      bool isPasswordInvalid,
      bool isConfirmPasswordInvalid,
      String phone,
      String verificationId,
      String email,
      String referral,
      String firstName,
      String lastName,
      String password,
      String confirmPassword});
}

/// @nodoc
class _$RegisterStateCopyWithImpl<$Res, $Val extends RegisterState>
    implements $RegisterStateCopyWith<$Res> {
  _$RegisterStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RegisterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? showPassword = null,
    Object? showConfirmPassword = null,
    Object? isEmailInvalid = null,
    Object? isPasswordInvalid = null,
    Object? isConfirmPasswordInvalid = null,
    Object? phone = null,
    Object? verificationId = null,
    Object? email = null,
    Object? referral = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? password = null,
    Object? confirmPassword = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      showPassword: null == showPassword
          ? _value.showPassword
          : showPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      showConfirmPassword: null == showConfirmPassword
          ? _value.showConfirmPassword
          : showConfirmPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailInvalid: null == isEmailInvalid
          ? _value.isEmailInvalid
          : isEmailInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordInvalid: null == isPasswordInvalid
          ? _value.isPasswordInvalid
          : isPasswordInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirmPasswordInvalid: null == isConfirmPasswordInvalid
          ? _value.isConfirmPasswordInvalid
          : isConfirmPasswordInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      verificationId: null == verificationId
          ? _value.verificationId
          : verificationId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      referral: null == referral
          ? _value.referral
          : referral // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      confirmPassword: null == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RegisterStateImplCopyWith<$Res>
    implements $RegisterStateCopyWith<$Res> {
  factory _$$RegisterStateImplCopyWith(
          _$RegisterStateImpl value, $Res Function(_$RegisterStateImpl) then) =
      __$$RegisterStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isSuccess,
      bool showPassword,
      bool showConfirmPassword,
      bool isEmailInvalid,
      bool isPasswordInvalid,
      bool isConfirmPasswordInvalid,
      String phone,
      String verificationId,
      String email,
      String referral,
      String firstName,
      String lastName,
      String password,
      String confirmPassword});
}

/// @nodoc
class __$$RegisterStateImplCopyWithImpl<$Res>
    extends _$RegisterStateCopyWithImpl<$Res, _$RegisterStateImpl>
    implements _$$RegisterStateImplCopyWith<$Res> {
  __$$RegisterStateImplCopyWithImpl(
      _$RegisterStateImpl _value, $Res Function(_$RegisterStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of RegisterState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isSuccess = null,
    Object? showPassword = null,
    Object? showConfirmPassword = null,
    Object? isEmailInvalid = null,
    Object? isPasswordInvalid = null,
    Object? isConfirmPasswordInvalid = null,
    Object? phone = null,
    Object? verificationId = null,
    Object? email = null,
    Object? referral = null,
    Object? firstName = null,
    Object? lastName = null,
    Object? password = null,
    Object? confirmPassword = null,
  }) {
    return _then(_$RegisterStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuccess: null == isSuccess
          ? _value.isSuccess
          : isSuccess // ignore: cast_nullable_to_non_nullable
              as bool,
      showPassword: null == showPassword
          ? _value.showPassword
          : showPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      showConfirmPassword: null == showConfirmPassword
          ? _value.showConfirmPassword
          : showConfirmPassword // ignore: cast_nullable_to_non_nullable
              as bool,
      isEmailInvalid: null == isEmailInvalid
          ? _value.isEmailInvalid
          : isEmailInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordInvalid: null == isPasswordInvalid
          ? _value.isPasswordInvalid
          : isPasswordInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      isConfirmPasswordInvalid: null == isConfirmPasswordInvalid
          ? _value.isConfirmPasswordInvalid
          : isConfirmPasswordInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
      phone: null == phone
          ? _value.phone
          : phone // ignore: cast_nullable_to_non_nullable
              as String,
      verificationId: null == verificationId
          ? _value.verificationId
          : verificationId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      referral: null == referral
          ? _value.referral
          : referral // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: null == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String,
      password: null == password
          ? _value.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
      confirmPassword: null == confirmPassword
          ? _value.confirmPassword
          : confirmPassword // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RegisterStateImpl extends _RegisterState {
  const _$RegisterStateImpl(
      {this.isLoading = false,
      this.isSuccess = false,
      this.showPassword = false,
      this.showConfirmPassword = false,
      this.isEmailInvalid = false,
      this.isPasswordInvalid = false,
      this.isConfirmPasswordInvalid = false,
      this.phone = '',
      this.verificationId = '',
      this.email = '',
      this.referral = '',
      this.firstName = '',
      this.lastName = '',
      this.password = '',
      this.confirmPassword = ''})
      : super._();

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isSuccess;
  @override
  @JsonKey()
  final bool showPassword;
  @override
  @JsonKey()
  final bool showConfirmPassword;
  @override
  @JsonKey()
  final bool isEmailInvalid;
  @override
  @JsonKey()
  final bool isPasswordInvalid;
  @override
  @JsonKey()
  final bool isConfirmPasswordInvalid;
  @override
  @JsonKey()
  final String phone;
  @override
  @JsonKey()
  final String verificationId;
  @override
  @JsonKey()
  final String email;
  @override
  @JsonKey()
  final String referral;
  @override
  @JsonKey()
  final String firstName;
  @override
  @JsonKey()
  final String lastName;
  @override
  @JsonKey()
  final String password;
  @override
  @JsonKey()
  final String confirmPassword;

  @override
  String toString() {
    return 'RegisterState(isLoading: $isLoading, isSuccess: $isSuccess, showPassword: $showPassword, showConfirmPassword: $showConfirmPassword, isEmailInvalid: $isEmailInvalid, isPasswordInvalid: $isPasswordInvalid, isConfirmPasswordInvalid: $isConfirmPasswordInvalid, phone: $phone, verificationId: $verificationId, email: $email, referral: $referral, firstName: $firstName, lastName: $lastName, password: $password, confirmPassword: $confirmPassword)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RegisterStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isSuccess, isSuccess) ||
                other.isSuccess == isSuccess) &&
            (identical(other.showPassword, showPassword) ||
                other.showPassword == showPassword) &&
            (identical(other.showConfirmPassword, showConfirmPassword) ||
                other.showConfirmPassword == showConfirmPassword) &&
            (identical(other.isEmailInvalid, isEmailInvalid) ||
                other.isEmailInvalid == isEmailInvalid) &&
            (identical(other.isPasswordInvalid, isPasswordInvalid) ||
                other.isPasswordInvalid == isPasswordInvalid) &&
            (identical(
                    other.isConfirmPasswordInvalid, isConfirmPasswordInvalid) ||
                other.isConfirmPasswordInvalid == isConfirmPasswordInvalid) &&
            (identical(other.phone, phone) || other.phone == phone) &&
            (identical(other.verificationId, verificationId) ||
                other.verificationId == verificationId) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.referral, referral) ||
                other.referral == referral) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.confirmPassword, confirmPassword) ||
                other.confirmPassword == confirmPassword));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isSuccess,
      showPassword,
      showConfirmPassword,
      isEmailInvalid,
      isPasswordInvalid,
      isConfirmPasswordInvalid,
      phone,
      verificationId,
      email,
      referral,
      firstName,
      lastName,
      password,
      confirmPassword);

  /// Create a copy of RegisterState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RegisterStateImplCopyWith<_$RegisterStateImpl> get copyWith =>
      __$$RegisterStateImplCopyWithImpl<_$RegisterStateImpl>(this, _$identity);
}

abstract class _RegisterState extends RegisterState {
  const factory _RegisterState(
      {final bool isLoading,
      final bool isSuccess,
      final bool showPassword,
      final bool showConfirmPassword,
      final bool isEmailInvalid,
      final bool isPasswordInvalid,
      final bool isConfirmPasswordInvalid,
      final String phone,
      final String verificationId,
      final String email,
      final String referral,
      final String firstName,
      final String lastName,
      final String password,
      final String confirmPassword}) = _$RegisterStateImpl;
  const _RegisterState._() : super._();

  @override
  bool get isLoading;
  @override
  bool get isSuccess;
  @override
  bool get showPassword;
  @override
  bool get showConfirmPassword;
  @override
  bool get isEmailInvalid;
  @override
  bool get isPasswordInvalid;
  @override
  bool get isConfirmPasswordInvalid;
  @override
  String get phone;
  @override
  String get verificationId;
  @override
  String get email;
  @override
  String get referral;
  @override
  String get firstName;
  @override
  String get lastName;
  @override
  String get password;
  @override
  String get confirmPassword;

  /// Create a copy of RegisterState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RegisterStateImplCopyWith<_$RegisterStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
