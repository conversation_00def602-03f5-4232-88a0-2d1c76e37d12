// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProductState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isAddLoading => throw _privateConstructorUsedError;
  bool get isShareLoading => throw _privateConstructorUsedError;
  bool get isCheckShopOrder => throw _privateConstructorUsedError;
  int get currentIndex => throw _privateConstructorUsedError;
  int get count => throw _privateConstructorUsedError;
  int get stockCount => throw _privateConstructorUsedError;
  List<TypedExtra> get typedExtras => throw _privateConstructorUsedError;
  List<Stocks> get initialStocks => throw _privateConstructorUsedError;
  List<int> get selectedIndexes => throw _privateConstructorUsedError;
  String get activeImageUrl => throw _privateConstructorUsedError;
  ProductData? get productData => throw _privateConstructorUsedError;
  Galleries? get selectImage => throw _privateConstructorUsedError;
  Stocks? get selectedStock => throw _privateConstructorUsedError;

  /// Create a copy of ProductState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductStateCopyWith<ProductState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductStateCopyWith<$Res> {
  factory $ProductStateCopyWith(
          ProductState value, $Res Function(ProductState) then) =
      _$ProductStateCopyWithImpl<$Res, ProductState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isAddLoading,
      bool isShareLoading,
      bool isCheckShopOrder,
      int currentIndex,
      int count,
      int stockCount,
      List<TypedExtra> typedExtras,
      List<Stocks> initialStocks,
      List<int> selectedIndexes,
      String activeImageUrl,
      ProductData? productData,
      Galleries? selectImage,
      Stocks? selectedStock});
}

/// @nodoc
class _$ProductStateCopyWithImpl<$Res, $Val extends ProductState>
    implements $ProductStateCopyWith<$Res> {
  _$ProductStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isAddLoading = null,
    Object? isShareLoading = null,
    Object? isCheckShopOrder = null,
    Object? currentIndex = null,
    Object? count = null,
    Object? stockCount = null,
    Object? typedExtras = null,
    Object? initialStocks = null,
    Object? selectedIndexes = null,
    Object? activeImageUrl = null,
    Object? productData = freezed,
    Object? selectImage = freezed,
    Object? selectedStock = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isAddLoading: null == isAddLoading
          ? _value.isAddLoading
          : isAddLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShareLoading: null == isShareLoading
          ? _value.isShareLoading
          : isShareLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckShopOrder: null == isCheckShopOrder
          ? _value.isCheckShopOrder
          : isCheckShopOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      currentIndex: null == currentIndex
          ? _value.currentIndex
          : currentIndex // ignore: cast_nullable_to_non_nullable
              as int,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      stockCount: null == stockCount
          ? _value.stockCount
          : stockCount // ignore: cast_nullable_to_non_nullable
              as int,
      typedExtras: null == typedExtras
          ? _value.typedExtras
          : typedExtras // ignore: cast_nullable_to_non_nullable
              as List<TypedExtra>,
      initialStocks: null == initialStocks
          ? _value.initialStocks
          : initialStocks // ignore: cast_nullable_to_non_nullable
              as List<Stocks>,
      selectedIndexes: null == selectedIndexes
          ? _value.selectedIndexes
          : selectedIndexes // ignore: cast_nullable_to_non_nullable
              as List<int>,
      activeImageUrl: null == activeImageUrl
          ? _value.activeImageUrl
          : activeImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      productData: freezed == productData
          ? _value.productData
          : productData // ignore: cast_nullable_to_non_nullable
              as ProductData?,
      selectImage: freezed == selectImage
          ? _value.selectImage
          : selectImage // ignore: cast_nullable_to_non_nullable
              as Galleries?,
      selectedStock: freezed == selectedStock
          ? _value.selectedStock
          : selectedStock // ignore: cast_nullable_to_non_nullable
              as Stocks?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductStateImplCopyWith<$Res>
    implements $ProductStateCopyWith<$Res> {
  factory _$$ProductStateImplCopyWith(
          _$ProductStateImpl value, $Res Function(_$ProductStateImpl) then) =
      __$$ProductStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isAddLoading,
      bool isShareLoading,
      bool isCheckShopOrder,
      int currentIndex,
      int count,
      int stockCount,
      List<TypedExtra> typedExtras,
      List<Stocks> initialStocks,
      List<int> selectedIndexes,
      String activeImageUrl,
      ProductData? productData,
      Galleries? selectImage,
      Stocks? selectedStock});
}

/// @nodoc
class __$$ProductStateImplCopyWithImpl<$Res>
    extends _$ProductStateCopyWithImpl<$Res, _$ProductStateImpl>
    implements _$$ProductStateImplCopyWith<$Res> {
  __$$ProductStateImplCopyWithImpl(
      _$ProductStateImpl _value, $Res Function(_$ProductStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isAddLoading = null,
    Object? isShareLoading = null,
    Object? isCheckShopOrder = null,
    Object? currentIndex = null,
    Object? count = null,
    Object? stockCount = null,
    Object? typedExtras = null,
    Object? initialStocks = null,
    Object? selectedIndexes = null,
    Object? activeImageUrl = null,
    Object? productData = freezed,
    Object? selectImage = freezed,
    Object? selectedStock = freezed,
  }) {
    return _then(_$ProductStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isAddLoading: null == isAddLoading
          ? _value.isAddLoading
          : isAddLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isShareLoading: null == isShareLoading
          ? _value.isShareLoading
          : isShareLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCheckShopOrder: null == isCheckShopOrder
          ? _value.isCheckShopOrder
          : isCheckShopOrder // ignore: cast_nullable_to_non_nullable
              as bool,
      currentIndex: null == currentIndex
          ? _value.currentIndex
          : currentIndex // ignore: cast_nullable_to_non_nullable
              as int,
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      stockCount: null == stockCount
          ? _value.stockCount
          : stockCount // ignore: cast_nullable_to_non_nullable
              as int,
      typedExtras: null == typedExtras
          ? _value._typedExtras
          : typedExtras // ignore: cast_nullable_to_non_nullable
              as List<TypedExtra>,
      initialStocks: null == initialStocks
          ? _value._initialStocks
          : initialStocks // ignore: cast_nullable_to_non_nullable
              as List<Stocks>,
      selectedIndexes: null == selectedIndexes
          ? _value._selectedIndexes
          : selectedIndexes // ignore: cast_nullable_to_non_nullable
              as List<int>,
      activeImageUrl: null == activeImageUrl
          ? _value.activeImageUrl
          : activeImageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      productData: freezed == productData
          ? _value.productData
          : productData // ignore: cast_nullable_to_non_nullable
              as ProductData?,
      selectImage: freezed == selectImage
          ? _value.selectImage
          : selectImage // ignore: cast_nullable_to_non_nullable
              as Galleries?,
      selectedStock: freezed == selectedStock
          ? _value.selectedStock
          : selectedStock // ignore: cast_nullable_to_non_nullable
              as Stocks?,
    ));
  }
}

/// @nodoc

class _$ProductStateImpl extends _ProductState {
  const _$ProductStateImpl(
      {this.isLoading = false,
      this.isAddLoading = false,
      this.isShareLoading = false,
      this.isCheckShopOrder = false,
      this.currentIndex = 0,
      this.count = 1,
      this.stockCount = 0,
      final List<TypedExtra> typedExtras = const [],
      final List<Stocks> initialStocks = const [],
      final List<int> selectedIndexes = const [],
      this.activeImageUrl = '',
      this.productData = null,
      this.selectImage = null,
      this.selectedStock})
      : _typedExtras = typedExtras,
        _initialStocks = initialStocks,
        _selectedIndexes = selectedIndexes,
        super._();

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isAddLoading;
  @override
  @JsonKey()
  final bool isShareLoading;
  @override
  @JsonKey()
  final bool isCheckShopOrder;
  @override
  @JsonKey()
  final int currentIndex;
  @override
  @JsonKey()
  final int count;
  @override
  @JsonKey()
  final int stockCount;
  final List<TypedExtra> _typedExtras;
  @override
  @JsonKey()
  List<TypedExtra> get typedExtras {
    if (_typedExtras is EqualUnmodifiableListView) return _typedExtras;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_typedExtras);
  }

  final List<Stocks> _initialStocks;
  @override
  @JsonKey()
  List<Stocks> get initialStocks {
    if (_initialStocks is EqualUnmodifiableListView) return _initialStocks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_initialStocks);
  }

  final List<int> _selectedIndexes;
  @override
  @JsonKey()
  List<int> get selectedIndexes {
    if (_selectedIndexes is EqualUnmodifiableListView) return _selectedIndexes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedIndexes);
  }

  @override
  @JsonKey()
  final String activeImageUrl;
  @override
  @JsonKey()
  final ProductData? productData;
  @override
  @JsonKey()
  final Galleries? selectImage;
  @override
  final Stocks? selectedStock;

  @override
  String toString() {
    return 'ProductState(isLoading: $isLoading, isAddLoading: $isAddLoading, isShareLoading: $isShareLoading, isCheckShopOrder: $isCheckShopOrder, currentIndex: $currentIndex, count: $count, stockCount: $stockCount, typedExtras: $typedExtras, initialStocks: $initialStocks, selectedIndexes: $selectedIndexes, activeImageUrl: $activeImageUrl, productData: $productData, selectImage: $selectImage, selectedStock: $selectedStock)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isAddLoading, isAddLoading) ||
                other.isAddLoading == isAddLoading) &&
            (identical(other.isShareLoading, isShareLoading) ||
                other.isShareLoading == isShareLoading) &&
            (identical(other.isCheckShopOrder, isCheckShopOrder) ||
                other.isCheckShopOrder == isCheckShopOrder) &&
            (identical(other.currentIndex, currentIndex) ||
                other.currentIndex == currentIndex) &&
            (identical(other.count, count) || other.count == count) &&
            (identical(other.stockCount, stockCount) ||
                other.stockCount == stockCount) &&
            const DeepCollectionEquality()
                .equals(other._typedExtras, _typedExtras) &&
            const DeepCollectionEquality()
                .equals(other._initialStocks, _initialStocks) &&
            const DeepCollectionEquality()
                .equals(other._selectedIndexes, _selectedIndexes) &&
            (identical(other.activeImageUrl, activeImageUrl) ||
                other.activeImageUrl == activeImageUrl) &&
            (identical(other.productData, productData) ||
                other.productData == productData) &&
            (identical(other.selectImage, selectImage) ||
                other.selectImage == selectImage) &&
            (identical(other.selectedStock, selectedStock) ||
                other.selectedStock == selectedStock));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      isAddLoading,
      isShareLoading,
      isCheckShopOrder,
      currentIndex,
      count,
      stockCount,
      const DeepCollectionEquality().hash(_typedExtras),
      const DeepCollectionEquality().hash(_initialStocks),
      const DeepCollectionEquality().hash(_selectedIndexes),
      activeImageUrl,
      productData,
      selectImage,
      selectedStock);

  /// Create a copy of ProductState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductStateImplCopyWith<_$ProductStateImpl> get copyWith =>
      __$$ProductStateImplCopyWithImpl<_$ProductStateImpl>(this, _$identity);
}

abstract class _ProductState extends ProductState {
  const factory _ProductState(
      {final bool isLoading,
      final bool isAddLoading,
      final bool isShareLoading,
      final bool isCheckShopOrder,
      final int currentIndex,
      final int count,
      final int stockCount,
      final List<TypedExtra> typedExtras,
      final List<Stocks> initialStocks,
      final List<int> selectedIndexes,
      final String activeImageUrl,
      final ProductData? productData,
      final Galleries? selectImage,
      final Stocks? selectedStock}) = _$ProductStateImpl;
  const _ProductState._() : super._();

  @override
  bool get isLoading;
  @override
  bool get isAddLoading;
  @override
  bool get isShareLoading;
  @override
  bool get isCheckShopOrder;
  @override
  int get currentIndex;
  @override
  int get count;
  @override
  int get stockCount;
  @override
  List<TypedExtra> get typedExtras;
  @override
  List<Stocks> get initialStocks;
  @override
  List<int> get selectedIndexes;
  @override
  String get activeImageUrl;
  @override
  ProductData? get productData;
  @override
  Galleries? get selectImage;
  @override
  Stocks? get selectedStock;

  /// Create a copy of ProductState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductStateImplCopyWith<_$ProductStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
