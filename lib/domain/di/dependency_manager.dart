import 'package:get_it/get_it.dart';
import 'package:google_place/google_place.dart';
import 'package:pandoo_delivery/domain/interface/address.dart';
import 'package:pandoo_delivery/domain/interface/auth.dart';
import 'package:pandoo_delivery/domain/interface/banners.dart';
import 'package:pandoo_delivery/domain/interface/blogs.dart';
import 'package:pandoo_delivery/domain/interface/brands.dart';
import 'package:pandoo_delivery/domain/interface/cart.dart';
import 'package:pandoo_delivery/domain/interface/categories.dart';
import 'package:pandoo_delivery/domain/interface/currencies.dart';
import 'package:pandoo_delivery/domain/interface/draw.dart';
import 'package:pandoo_delivery/domain/interface/gallery.dart';
import 'package:pandoo_delivery/domain/interface/notification.dart';
import 'package:pandoo_delivery/domain/interface/orders.dart';
import 'package:pandoo_delivery/domain/interface/parcel.dart';
import 'package:pandoo_delivery/domain/interface/payments.dart';
import 'package:pandoo_delivery/domain/interface/products.dart';
import 'package:pandoo_delivery/domain/interface/settings.dart';
import 'package:pandoo_delivery/domain/interface/shops.dart';
import 'package:pandoo_delivery/domain/interface/user.dart';
import 'package:pandoo_delivery/infrastructure/repository/address_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/auth_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/banners_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/blogs_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/brands_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/cart_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/categories_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/currencies_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/draw_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/gallery_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/notification_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/orders_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/parcel_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/payments_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/products_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/settings_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/shops_repository.dart';
import 'package:pandoo_delivery/infrastructure/repository/user_repository.dart';
import 'package:pandoo_delivery/app_constants.dart';
import 'package:pandoo_delivery/infrastructure/services/local_storage.dart';
import '../handlers/http_service.dart';

final GetIt getIt = GetIt.instance;

Future<void> setUpDependencies() async {
  getIt.registerLazySingleton<HttpService>(() => HttpService());
  getIt.registerSingleton<SettingsRepositoryFacade>(SettingsRepository());
  getIt.registerSingleton<AuthRepositoryFacade>(AuthRepository());
  getIt.registerSingleton<ProductsRepositoryFacade>(ProductsRepository());
  getIt.registerSingleton<ShopsRepositoryFacade>(ShopsRepository());
  getIt.registerSingleton<BrandsRepositoryFacade>(BrandsRepository());
  getIt.registerSingleton<GalleryRepositoryFacade>(GalleryRepository());
  getIt.registerSingleton<CategoriesRepositoryFacade>(CategoriesRepository());
  getIt.registerSingleton<CurrenciesRepositoryFacade>(CurrenciesRepository());
  getIt.registerSingleton<AddressRepositoryFacade>(AddressRepository());
  getIt.registerSingleton<BannersRepositoryFacade>(BannersRepository());
  getIt.registerSingleton<GooglePlace>(GooglePlace(AppConstants.googleApiKey));
  getIt.registerSingleton<PaymentsRepositoryFacade>(PaymentsRepository());
  getIt.registerSingleton<OrdersRepositoryFacade>(OrdersRepository());
  getIt.registerSingleton<UserRepositoryFacade>(UserRepository());
  getIt.registerSingleton<BlogsRepositoryFacade>(BlogsRepository());
  getIt.registerSingleton<CartRepositoryFacade>(CartRepository());
  getIt.registerSingleton<DrawRepositoryFacade>(DrawRepository());
  getIt.registerSingleton<ParcelRepositoryFacade>(ParcelRepository());
  getIt.registerSingleton<NotificationRepositoryFacade>(NotificationRepositoryImpl());
  getIt.registerSingleton<Map>(LocalStorage.getTranslations());
}

final dioHttp = getIt.get<HttpService>();
final notificationRepo = getIt.get<NotificationRepositoryFacade>();
final drawRepository = getIt.get<DrawRepositoryFacade>();
final settingsRepository = getIt.get<SettingsRepositoryFacade>();
final authRepository = getIt.get<AuthRepositoryFacade>();
final productsRepository = getIt.get<ProductsRepositoryFacade>();
final shopsRepository = getIt.get<ShopsRepositoryFacade>();
final brandsRepository = getIt.get<BrandsRepositoryFacade>();
final galleryRepository = getIt.get<GalleryRepositoryFacade>();
final categoriesRepository = getIt.get<CategoriesRepositoryFacade>();
final currenciesRepository = getIt.get<CurrenciesRepositoryFacade>();
final addressesRepository = getIt.get<AddressRepositoryFacade>();
final bannersRepository = getIt.get<BannersRepositoryFacade>();
final googlePlace = getIt.get<GooglePlace>();
final paymentsRepository = getIt.get<PaymentsRepositoryFacade>();
final ordersRepository = getIt.get<OrdersRepositoryFacade>();
final userRepository = getIt.get<UserRepositoryFacade>();
final blogsRepository = getIt.get<BlogsRepositoryFacade>();
final cartRepository = getIt.get<CartRepositoryFacade>();
final parcelRepository = getIt.get<ParcelRepositoryFacade>();
final translation = getIt.get<Map>();
