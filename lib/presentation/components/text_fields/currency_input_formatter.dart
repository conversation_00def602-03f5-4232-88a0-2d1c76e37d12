import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class CurrencyInputFormatter extends TextInputFormatter {
  final String locale;
  final String symbol;
  final int decimalDigits;
  
  CurrencyInputFormatter({
    this.locale = 'pt_BR',
    this.symbol = 'R\$',
    this.decimalDigits = 2,
  });

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue.copyWith(text: '');
    }

    // Remove all non-digit characters
    String digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    if (digitsOnly.isEmpty) {
      return newValue.copyWith(text: '');
    }

    // Convert to double (considering decimal places)
    // For better UX: if user types "10", it becomes R$ 10,00 (not R$ 0,10)
    double value;
    if (digitsOnly.length <= 2) {
      // For 1-2 digits, treat as whole currency units
      value = double.parse(digitsOnly);
    } else {
      // For 3+ digits, treat last 2 as cents
      value = double.parse(digitsOnly) / 100;
    }

    // Format as currency
    final formatter = NumberFormat.currency(
      locale: locale,
      symbol: symbol,
      decimalDigits: decimalDigits,
    );

    String formatted = formatter.format(value);

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
  
  /// Extract numeric value from formatted text
  static double getNumericValue(String formattedText) {
    if (formattedText.isEmpty) return 0.0;
    
    // Remove currency symbol and spaces
    String digitsOnly = formattedText.replaceAll(RegExp(r'[^\d,.]'), '');
    
    // Handle Brazilian format (1.000,50)
    if (digitsOnly.contains(',')) {
      // Replace dots (thousand separators) and comma (decimal separator)
      digitsOnly = digitsOnly.replaceAll('.', '').replaceAll(',', '.');
    }
    
    return double.tryParse(digitsOnly) ?? 0.0;
  }
}
