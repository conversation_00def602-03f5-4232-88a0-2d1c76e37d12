// ignore_for_file: use_build_context_synchronously
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pandoo_delivery/application/language/language_provider.dart';
import 'package:pandoo_delivery/application/main/main_provider.dart';
import 'package:pandoo_delivery/app_constants.dart';
import 'package:pandoo_delivery/infrastructure/services/app_helpers.dart';
import 'package:pandoo_delivery/infrastructure/services/local_storage.dart';
import 'package:pandoo_delivery/infrastructure/services/tr_keys.dart';
import 'package:pandoo_delivery/presentation/components/buttons/custom_button.dart';
import 'package:pandoo_delivery/presentation/pages/auth/register/register_page.dart';
import 'package:pandoo_delivery/presentation/routes/app_router.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';
import 'package:pandoo_delivery/application/auth/auth.dart';
// import '../../profile/language_page.dart'; // Removed - language selection disabled for Brazilian users
import 'login_screen.dart';


@RoutePage()
class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(loginProvider.notifier).checkLanguage(context);
    });
    super.initState();
  }



  // Language selection removed for Brazilian users - defaults are automatically set
  // void selectLanguage() {
  //   AppHelpers.showCustomModalBottomSheet(
  //       isDismissible: false,
  //       isDrag: false,
  //       context: context,
  //       modal: LanguageScreen(
  //         onSave: () {
  //           Navigator.pop(context);
  //         },
  //       ),
  //       isDarkMode: false);
  // }

  @override
  Widget build(BuildContext context) {
    ref.watch(languageProvider);
    // Language selection removed - Brazilian defaults are automatically set
    // ref.listen(loginProvider, (previous, next) {
    //   if (!next.isSelectLanguage &&
    //       !((previous?.isSelectLanguage ?? false) == next.isSelectLanguage)) {
    //     selectLanguage();
    //   }
    // });

    final bool isDarkMode = LocalStorage.getAppThemeMode();
    final bool isLtr = LocalStorage.getLangLtr();
    return Directionality(
      textDirection: isLtr ? TextDirection.ltr : TextDirection.rtl,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor:
        isDarkMode ? AppStyle.dontHaveAnAccBackDark : AppStyle.white,
        body: Container(
          decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  "assets/images/splash.png",
                ),
                fit: BoxFit.fill,
              )),
          child: SafeArea(
            child: Padding(
              padding: REdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          AppHelpers.getAppName() ?? "",
                          style: AppStyle.interSemi(color: AppStyle.white),
                        ),
                      ),
                      8.horizontalSpace,
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          ref.read(mainProvider.notifier).selectIndex(0);
                          if (AppConstants.isDemo) {
                            context.pushRoute(UiTypeRoute());
                            return;
                          }
                          context.replaceRoute(const MainRoute());
                        },
                        child: Text(
                          AppHelpers.getTranslation(TrKeys.skip),
                          style: AppStyle.interSemi(
                            size: 16.sp,
                            color: AppStyle.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      CustomButton(
                        title: AppHelpers.getTranslation(TrKeys.login),
                        onPressed: () {
                          AppHelpers.showCustomModalBottomSheet(
                            context: context,
                            modal: const LoginScreen(),
                            isDarkMode: isDarkMode,
                          );
                        },
                      ),
                      10.verticalSpace,
                      CustomButton(
                        title: AppHelpers.getTranslation(TrKeys.register),
                        onPressed: () {
                          AppHelpers.showCustomModalBottomSheet(
                              context: context,
                              modal: RegisterPage(
                                isOnlyEmail: true,
                              ),
                              isDarkMode: isDarkMode,
                              paddingTop: MediaQuery.paddingOf(context).top);
                        },
                        background: AppStyle.transparent,
                        textColor: AppStyle.white,
                        borderColor: AppStyle.white,
                      ),
                      22.verticalSpace,
                    ],
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
