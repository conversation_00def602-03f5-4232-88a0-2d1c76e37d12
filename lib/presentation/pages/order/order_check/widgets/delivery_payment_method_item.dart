import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pandoo_delivery/infrastructure/models/data/delivery_payment_method.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';

class DeliveryPaymentMethodItem extends StatelessWidget {
  final DeliveryPaymentMethod method;
  final bool isSelected;
  final VoidCallback onTap;

  const DeliveryPaymentMethodItem({
    super.key,
    required this.method,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: 8.h),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: isSelected ? AppStyle.primary.withOpacity(0.1) : AppStyle.white,
          border: Border.all(
            color: isSelected ? AppStyle.primary : AppStyle.borderColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            // Payment Method Icon
            Container(
              width: 36.w,
              height: 36.h,
              decoration: BoxDecoration(
                color: AppStyle.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Center(
                child: Text(
                  method.icon ?? '💳',
                  style: TextStyle(fontSize: 18.sp),
                ),
              ),
            ),

            12.horizontalSpace,

            // Payment Method Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    method.name ?? '',
                    style: AppStyle.interSemi(
                      size: 15,
                      color: AppStyle.black,
                    ),
                  ),
                  2.verticalSpace,
                  Text(
                    method.description ?? '',
                    style: AppStyle.interRegular(
                      size: 12,
                      color: AppStyle.textGrey,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // Selection Indicator
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppStyle.primary,
                size: 20.r,
              ),
          ],
        ),
      ),
    );
  }
}
