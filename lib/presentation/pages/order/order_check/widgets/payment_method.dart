import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pandoo_delivery/application/payment_methods/payment_provider.dart';
import 'package:pandoo_delivery/application/payment_methods/payment_state.dart';
import 'package:pandoo_delivery/infrastructure/models/data/payment_data.dart';
import 'package:pandoo_delivery/infrastructure/services/app_helpers.dart';
import 'package:pandoo_delivery/infrastructure/services/tr_keys.dart';
import 'package:pandoo_delivery/presentation/components/buttons/custom_button.dart';
import 'package:pandoo_delivery/presentation/components/loading.dart';
import 'package:pandoo_delivery/presentation/components/title_icon.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';
import 'package:pandoo_delivery/application/payment_methods/payment_notifier.dart';
import 'package:pandoo_delivery/infrastructure/services/local_storage.dart';
import 'package:pandoo_delivery/presentation/components/select_item.dart';
import 'package:pandoo_delivery/presentation/components/text_fields/custom_text_field.dart';
import 'package:pandoo_delivery/presentation/pages/order/order_check/widgets/change_option_button.dart';

class PaymentMethods extends ConsumerStatefulWidget {
  final ValueChanged<PaymentData>? payLater;
  final Function(PaymentData, num)? tips;
  final num? tipPrice;
  final double? orderTotal;

  const PaymentMethods({this.payLater, this.tips, this.tipPrice, this.orderTotal, super.key});

  @override
  ConsumerState<PaymentMethods> createState() => _PaymentMethodsState();
}

class _PaymentMethodsState extends ConsumerState<PaymentMethods> {
  final bool isLtr = LocalStorage.getLangLtr();
  late PaymentNotifier event;
  late PaymentState state;
  final TextEditingController cashAmountController = TextEditingController();
  bool changeRequired = false;
  double cashPaymentAmount = 0.0;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(paymentProvider.notifier)
          .fetchPayments(context, withOutCash: widget.tipPrice != null);
    });
    super.initState();
  }

  @override
  void dispose() {
    cashAmountController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    event = ref.read(paymentProvider.notifier);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    state = ref.watch(paymentProvider);
    return Directionality(
      textDirection: isLtr ? TextDirection.ltr : TextDirection.rtl,
      child: Container(
        decoration: BoxDecoration(
            color: AppStyle.bgGrey.withOpacity(0.96),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12.r),
              topRight: Radius.circular(12.r),
            )),
        width: double.infinity,
        child: state.isPaymentsLoading
            ? const Loading()
            : SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          8.verticalSpace,
                          Center(
                            child: Container(
                              height: 4.h,
                              width: 48.w,
                              decoration: BoxDecoration(
                                  color: AppStyle.dragElement,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(40.r))),
                            ),
                          ),
                          14.verticalSpace,
                          TitleAndIcon(
                            title: AppHelpers.getTranslation(
                                TrKeys.paymentMethods),
                          ),
                          24.verticalSpace,
                          (state.payments.isNotEmpty)
                              ? ListView.builder(
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemCount: state.payments.length,
                                  itemBuilder: (context, index) {
                                    return Column(
                                      children: [
                                        SelectItem(
                                          onTap: () => event.change(index),
                                          isActive: state.currentIndex == index,
                                          title: AppHelpers.getTranslation(
                                              state.payments[index].tag ?? ""),
                                        ),
                                        // Show cash change options if cash delivery is selected
                                        if (_isCashDeliverySelected(state, index)) ...[
                                          16.verticalSpace,
                                          _buildCashChangeSection(),
                                        ],
                                      ],
                                    );
                                  })
                              : Center(
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        bottom: 32.h, left: 24.w, right: 24.w),
                                    child: Text(
                                      AppHelpers.getTranslation(
                                          TrKeys.paymentTypeIsNotAdded),
                                      style: AppStyle.interSemi(
                                        size: 16,
                                        color: AppStyle.textGrey,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ),
                          if (widget.payLater != null)
                            Padding(
                              padding: EdgeInsets.only(bottom: 32.r),
                              child: CustomButton(
                                  title: AppHelpers.getTranslation(TrKeys.pay),
                                  onPressed: () {
                                    context.maybePop();
                                    widget.payLater?.call(PaymentData(
                                        id: state
                                            .payments[state.currentIndex].id,
                                        tag: AppHelpers.getTranslation(state
                                            .payments[state.currentIndex]
                                            .tag)));
                                  }),
                            ),
                          if (widget.tips != null)
                            Padding(
                              padding: EdgeInsets.only(bottom: 32.r),
                              child: CustomButton(
                                  title: AppHelpers.getTranslation(TrKeys.pay),
                                  onPressed: () {
                                    context.maybePop();
                                    widget.tips?.call(
                                      PaymentData(
                                          id: state
                                              .payments[state.currentIndex].id,
                                          tag: AppHelpers.getTranslation(state
                                              .payments[state.currentIndex]
                                              .tag)),
                                      widget.tipPrice ?? 0,
                                    );
                                  }),
                            )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  bool _isCashDeliverySelected(PaymentState state, int index) {
    if (state.currentIndex != index) return false;
    final paymentTag = state.payments[index].tag?.toLowerCase() ?? '';
    return paymentTag.contains('cash') || paymentTag.contains('dinheiro');
  }

  Widget _buildCashChangeSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppStyle.bgGrey,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppStyle.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppHelpers.getTranslation(TrKeys.needChange),
            style: AppStyle.interSemi(size: 16),
          ),
          12.verticalSpace,

          // Change Required Toggle
          Row(
            children: [
              Expanded(
                child: ChangeOptionButton(
                  title: AppHelpers.getTranslation(TrKeys.yes),
                  isSelected: changeRequired,
                  onTap: () {
                    setState(() {
                      changeRequired = true;
                    });
                  },
                ),
              ),
              12.horizontalSpace,
              Expanded(
                child: ChangeOptionButton(
                  title: AppHelpers.getTranslation(TrKeys.no),
                  isSelected: !changeRequired,
                  onTap: () {
                    setState(() {
                      changeRequired = false;
                      cashAmountController.clear();
                      cashPaymentAmount = 0.0;
                    });
                  },
                ),
              ),
            ],
          ),

          // Cash Amount Input
          if (changeRequired) ...[
            16.verticalSpace,
            Text(
              AppHelpers.getTranslation(TrKeys.cashPaymentAmount),
              style: AppStyle.interNormal(size: 14),
            ),
            4.verticalSpace,
            Text(
              AppHelpers.getTranslation(TrKeys.cashPaymentAmountHint),
              style: AppStyle.interRegular(size: 12, color: AppStyle.textGrey),
            ),
            8.verticalSpace,
            CustomTextField(
              controller: cashAmountController,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              prefix: Text(
                'R\$ ',
                style: AppStyle.interNormal(size: 16, color: AppStyle.primary),
              ),
              hint: widget.orderTotal != null
                  ? AppHelpers.numberFormat(number: widget.orderTotal!)
                  : '0,00',
              onChanged: (value) {
                final amount = double.tryParse(value.replaceAll(',', '.')) ?? 0.0;
                setState(() {
                  cashPaymentAmount = amount;
                });
              },
            ),

            // Validation and Change Display
            if (cashPaymentAmount > 0 && widget.orderTotal != null) ...[
              8.verticalSpace,
              if (cashPaymentAmount < widget.orderTotal!) ...[
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: AppStyle.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: AppStyle.red.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: AppStyle.red, size: 16.r),
                      8.horizontalSpace,
                      Expanded(
                        child: Text(
                          AppHelpers.getTranslation(TrKeys.cashAmountMustBeGreater),
                          style: AppStyle.interRegular(size: 12, color: AppStyle.red),
                        ),
                      ),
                    ],
                  ),
                ),
              ] else ...[
                Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: AppStyle.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: AppStyle.primary.withOpacity(0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppHelpers.getTranslation(TrKeys.changeSummary),
                        style: AppStyle.interSemi(size: 14, color: AppStyle.primary),
                      ),
                      8.verticalSpace,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            AppHelpers.getTranslation(TrKeys.changeToReturn),
                            style: AppStyle.interRegular(size: 14, color: AppStyle.black),
                          ),
                          Text(
                            AppHelpers.numberFormat(number: cashPaymentAmount - widget.orderTotal!),
                            style: AppStyle.interSemi(size: 16, color: AppStyle.primary),
                          ),
                        ],
                      ),
                      4.verticalSpace,
                      Text(
                        AppHelpers.getTranslation(TrKeys.driverWillReturn),
                        style: AppStyle.interRegular(size: 12, color: AppStyle.textGrey),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ],
        ],
      ),
    );
  }
}
