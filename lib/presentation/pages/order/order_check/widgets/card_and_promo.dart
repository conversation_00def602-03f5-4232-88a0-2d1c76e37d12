import 'package:flutter/material.dart';
import 'package:flutter_remix/flutter_remix.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pandoo_delivery/application/delivery_payment/delivery_payment_provider.dart';
import 'package:pandoo_delivery/application/order/order_provider.dart';
import 'package:pandoo_delivery/application/payment_methods/payment_provider.dart';
import 'package:pandoo_delivery/application/shop_order/shop_order_provider.dart';
import 'package:pandoo_delivery/infrastructure/models/data/delivery_payment_method.dart';
import 'package:pandoo_delivery/infrastructure/models/data/order_body_data.dart';
import 'package:pandoo_delivery/infrastructure/models/data/payment_data.dart';
import 'package:pandoo_delivery/infrastructure/models/data/shop_data.dart';
import 'package:pandoo_delivery/app_constants.dart';
import 'package:pandoo_delivery/infrastructure/services/app_helpers.dart';
import 'package:pandoo_delivery/infrastructure/services/enums.dart';
import 'package:pandoo_delivery/infrastructure/services/local_storage.dart';
import 'package:pandoo_delivery/infrastructure/services/tr_keys.dart';
import 'package:pandoo_delivery/presentation/pages/order/order_check/widgets/promo_code.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';

import 'order_payment_container.dart';
import 'two_step_payment_modal.dart';

class CardAndPromo extends StatelessWidget {
  const CardAndPromo({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Consumer(builder: (context, ref, child) {
            final orderState = ref.watch(orderProvider);
            final deliveryPaymentState = ref.watch(deliveryPaymentProvider);

            // Check if a delivery payment method is selected
            final selectedDeliveryMethod = deliveryPaymentState.selectedMethodForUI;

            return OrderPaymentContainer(
              onTap: () {
                AppHelpers.showCustomModalBottomSheet(
                  paddingTop: MediaQuery.paddingOf(context).top,
                  context: context,
                  modal: TwoStepPaymentModal(
                    shopId: orderState.shopData?.id,
                    orderTotal: orderState.calculateData?.totalPrice?.toDouble(),
                    onPaymentSelected: (paymentData) {
                      // Handle payment method selection (update UI only)
                      _updatePaymentMethodSelection(context, ref, paymentData);
                    },
                  ),
                  isDarkMode: false,
                  isDrag: true,
                  radius: 12,
                );
              },
              icon: Icon(
                selectedDeliveryMethod != null
                    ? _getPaymentMethodIconData(selectedDeliveryMethod.tag)
                    : FlutterRemix.bank_card_fill,
                color: selectedDeliveryMethod != null
                    ? AppStyle.primary
                    : ((AppHelpers.getPaymentType() == "admin")
                            ? (ref.watch(paymentProvider).payments.isNotEmpty)
                            : (ref
                                    .watch(orderProvider)
                                    .shopData
                                    ?.shopPayments
                                    ?.isNotEmpty ??
                                false))
                        ? AppStyle.primary
                        : AppStyle.black,
              ),
              title: selectedDeliveryMethod != null
                  ? selectedDeliveryMethod.name ?? selectedDeliveryMethod.tag ?? 'Delivery Payment'
                  : ((AppHelpers.getPaymentType() == "admin")
                          ? (ref.watch(paymentProvider).payments.isNotEmpty)
                          : (ref
                                  .watch(orderProvider)
                                  .shopData
                                  ?.shopPayments
                                  ?.isNotEmpty ??
                              false))
                      ? ((AppHelpers.getPaymentType() == "admin")
                          ? (ref
                              .watch(paymentProvider)
                              .payments[ref.watch(paymentProvider).currentIndex]
                              .tag)
                          : (ref
                                  .watch(orderProvider)
                                  .shopData
                                  ?.shopPayments?[
                                      ref.watch(paymentProvider).currentIndex]
                                  ?.payment
                                  ?.tag ??
                              ""))
                      : AppHelpers.getTranslation(TrKeys.noPaymentType),
              isActive: selectedDeliveryMethod != null ||
                  ((AppHelpers.getPaymentType() == "admin")
                      ? (ref.watch(paymentProvider).payments.isNotEmpty)
                      : (ref
                              .watch(orderProvider)
                              .shopData
                              ?.shopPayments
                              ?.isNotEmpty ??
                          false)),
            );
          }),
          Consumer(builder: (context, ref, child) {
            return OrderPaymentContainer(
              onTap: () {
                AppHelpers.showCustomModalBottomSheet(
                  context: context,
                  modal: const PromoCodeScreen(),
                  isDarkMode: false,
                  isDrag: true,
                  radius: 12,
                );
              },
              isActive: ref.watch(orderProvider).promoCode != null,
              icon: Icon(
                FlutterRemix.ticket_line,
                color: ref.watch(orderProvider).promoCode == null
                    ? AppStyle.black
                    : AppStyle.primary,
              ),
              title: ref.watch(orderProvider).promoCode ??
                  AppHelpers.getTranslation(TrKeys.youHavePromoCode),
            );
          }),
        ],
      ),
    );
  }

  static void _updatePaymentMethodSelection(
    BuildContext context,
    WidgetRef ref,
    Map<String, dynamic> paymentData
  ) {
    // Safety check to ensure context is still mounted
    if (!context.mounted) return;

    // Create a DeliveryPaymentMethod object from the payment data
    final selectedMethod = DeliveryPaymentMethod(
      tag: paymentData['payment_method'],
      name: paymentData['method_name'] ?? paymentData['payment_method'],
      description: paymentData['method_description'] ?? '',
      supportsChange: paymentData['is_cash'] == true,
      icon: _getPaymentMethodIcon(paymentData['payment_method']),
    );

    // Update the delivery payment state with the selected method for UI display
    ref.read(deliveryPaymentProvider.notifier).setSelectedMethodForUI(selectedMethod);

    // Check if this is just a payment method selection (not order completion)
    if (paymentData['requires_confirmation'] != true) {
      // Show confirmation message for payment method selection
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Método de pagamento selecionado: ${paymentData['method_name'] ?? paymentData['payment_method']}'),
          duration: const Duration(seconds: 2),
          backgroundColor: AppStyle.primary,
        ),
      );

      return;
    }

    // If requires_confirmation is true, this is a cash payment with change details
    // Still only update the UI, don't complete the order
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Pagamento em dinheiro configurado: ${paymentData['method_name'] ?? paymentData['payment_method']}'),
        duration: const Duration(seconds: 2),
        backgroundColor: AppStyle.primary,
      ),
    );
  }

  static String _getPaymentMethodIcon(String? paymentMethod) {
    switch (paymentMethod) {
      // Handle API tags from delivery payment methods
      case 'pix_delivery':
      case 'pix':
        return '📱'; // QR code/smartphone icon for PIX
      case 'card_delivery':
      case 'credit_card':
        return '💳'; // Credit card icon
      case 'debit_delivery':
      case 'debit_card':
        return '💳'; // Debit card icon
      case 'cash_delivery':
      case 'cash':
      default:
        return '💵'; // Paper money icon for cash
    }
  }

  static IconData _getPaymentMethodIconData(String? paymentMethod) {
    switch (paymentMethod) {
      // Handle API tags from delivery payment methods
      case 'pix_delivery':
      case 'pix':
        return FlutterRemix.qr_scan_2_line; // QR scan icon for PIX
      case 'card_delivery':
      case 'credit_card':
        return FlutterRemix.bank_card_2_line; // Card terminal/POS icon for credit card
      case 'debit_delivery':
      case 'debit_card':
        return FlutterRemix.bank_card_2_fill; // Card terminal/POS icon for debit card (filled version for distinction)
      case 'cash_delivery':
      case 'cash':
      default:
        return FlutterRemix.bill_line; // Generic bill/banknote icon for cash (no currency symbol)
    }
  }

  // This method will be called from the main checkout button
  static void processOrderWithSelectedPayment(
    BuildContext context,
    WidgetRef ref,
    Map<String, dynamic> paymentData
  ) {
    // Create a PaymentData object for delivery payments
    final paymentMethod = PaymentData(
      tag: paymentData['payment_method'],
      id: paymentData['payment_id'], // Will be null for delivery payments
    );

    // Get order state
    final orderState = ref.read(orderProvider);
    final shopOrderState = ref.read(shopOrderProvider);

    // Get current address or use default
    final selectedAddress = LocalStorage.getAddressSelected();
    final defaultLocation = Location(
      longitude: selectedAddress?.location?.longitude ?? AppConstants.demoLongitude,
      latitude: selectedAddress?.location?.latitude ?? AppConstants.demoLatitude,
    );

    final defaultAddress = AddressModel(
      address: selectedAddress?.address ?? 'Endereço não informado',
      house: orderState.house,
      floor: orderState.floor,
      office: orderState.office,
    );

    // Create order with delivery payment data
    ref.read(orderProvider.notifier).createOrder(
      context: context,
      data: OrderBodyData(
        paymentId: paymentData['payment_id'], // Will be null for delivery payments
        username: orderState.username,
        phone: orderState.phoneNumber ?? LocalStorage.getUser()?.phone ?? '',
        notes: orderState.notes,
        cartId: shopOrderState.cart?.id ?? 0,
        shopId: orderState.shopData?.id ?? 0,
        coupon: orderState.promoCode,
        deliveryFee: orderState.calculateData?.deliveryFee ?? 0,
        deliveryType: orderState.tabIndex == 0
            ? DeliveryTypeEnum.delivery
            : DeliveryTypeEnum.pickup,
        // Required location and address fields
        location: defaultLocation,
        address: defaultAddress,
        deliveryDate: "${orderState.selectDate?.year ?? DateTime.now().year}-${(orderState.selectDate?.month ?? DateTime.now().month).toString().padLeft(2, '0')}-${(orderState.selectDate?.day ?? DateTime.now().day).toString().padLeft(2, '0')}",
        deliveryTime: orderState.selectTime.hour.toString().length == 2
            ? "${orderState.selectTime.hour}:${orderState.selectTime.minute.toString().padLeft(2, '0')}"
            : "0${orderState.selectTime.hour}:${orderState.selectTime.minute.toString().padLeft(2, '0')}",
        // Add delivery payment specific fields
        paymentMethod: paymentData['payment_method'],
        changeRequired: paymentData['change_required'] ?? false,
        changeAmount: paymentData['change_amount'],
        cashPaymentAmount: paymentData['cash_payment_amount'],
        paymentNotes: paymentData['payment_notes'],
      ),
      payment: paymentMethod,
      onSuccess: () {
        // Clear cart and refresh orders
        ref.read(shopOrderProvider.notifier).getCart(context, () {});
      },
    );
  }
}
