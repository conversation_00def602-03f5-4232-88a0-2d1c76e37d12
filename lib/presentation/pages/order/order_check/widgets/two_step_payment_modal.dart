import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pandoo_delivery/infrastructure/services/app_helpers.dart';
import 'package:pandoo_delivery/infrastructure/services/tr_keys.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';
import 'package:pandoo_delivery/presentation/components/buttons/custom_button.dart';
import 'package:pandoo_delivery/presentation/pages/order/order_check/widgets/payment_type_selector.dart';
import 'package:pandoo_delivery/presentation/pages/order/order_check/widgets/delivery_payment_methods.dart';
import 'package:pandoo_delivery/presentation/pages/order/order_check/widgets/online_payment_methods.dart';

enum PaymentStep { selectType, selectMethod }

class TwoStepPaymentModal extends ConsumerStatefulWidget {
  final int? shopId;
  final double? orderTotal;
  final Function(Map<String, dynamic>)? onPaymentSelected;

  const TwoStepPaymentModal({
    super.key,
    this.shopId,
    this.orderTotal,
    this.onPaymentSelected,
  });

  @override
  ConsumerState<TwoStepPaymentModal> createState() => _TwoStepPaymentModalState();
}

class _TwoStepPaymentModalState extends ConsumerState<TwoStepPaymentModal> {
  PaymentStep currentStep = PaymentStep.selectType;
  String selectedPaymentType = '';
  Map<String, dynamic>? selectedPaymentData;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomPadding = MediaQuery.of(context).padding.bottom;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      constraints: BoxConstraints(
        maxHeight: currentStep == PaymentStep.selectType
            ? screenHeight * 0.5  // Smaller for initial selection
            : screenHeight * 0.75, // Larger for payment methods
        minHeight: currentStep == PaymentStep.selectType
            ? screenHeight * 0.4
            : screenHeight * 0.6,
      ),
      margin: EdgeInsets.only(bottom: bottomPadding + 16.h), // Avoid navigation bar
      decoration: BoxDecoration(
        color: AppStyle.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 12.h),
            decoration: BoxDecoration(
              color: AppStyle.borderColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.all(24.w),
            child: Row(
              children: [
                if (currentStep == PaymentStep.selectMethod)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        currentStep = PaymentStep.selectType;
                        selectedPaymentType = '';
                        selectedPaymentData = null;
                      });
                    },
                    child: Icon(
                      Icons.arrow_back,
                      size: 24.r,
                      color: AppStyle.black,
                    ),
                  ),
                if (currentStep == PaymentStep.selectMethod) 16.horizontalSpace,
                Expanded(
                  child: Text(
                    _getHeaderTitle(),
                    style: AppStyle.interSemi(size: 18),
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Icon(
                    Icons.close,
                    size: 24.r,
                    color: AppStyle.black,
                  ),
                ),
              ],
            ),
          ),

          // Content with smooth animations
          Flexible(
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 400),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0.3, 0.0),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeInOut,
                  )),
                  child: FadeTransition(
                    opacity: animation,
                    child: child,
                  ),
                );
              },
              child: SingleChildScrollView(
                key: ValueKey(currentStep),
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: _buildStepContent(),
              ),
            ),
          ),

          // Bottom action with safe area padding
          if (selectedPaymentData != null &&
              selectedPaymentData!['requires_confirmation'] == true)
            Container(
              padding: EdgeInsets.fromLTRB(24.w, 16.h, 24.w, 24.h),
              child: _buildBottomAction(),
            ),
        ],
      ),
    );
  }

  String _getHeaderTitle() {
    switch (currentStep) {
      case PaymentStep.selectType:
        return AppHelpers.getTranslation(TrKeys.selectPaymentMethod);
      case PaymentStep.selectMethod:
        if (selectedPaymentType == 'pay_now') {
          return AppHelpers.getTranslation(TrKeys.payNow);
        } else {
          return AppHelpers.getTranslation(TrKeys.payOnDelivery);
        }
    }
  }

  Widget _buildStepContent() {
    switch (currentStep) {
      case PaymentStep.selectType:
        return PaymentTypeSelector(
          shopId: widget.shopId,
          orderTotal: widget.orderTotal,
          onPayNowSelected: () {
            setState(() {
              currentStep = PaymentStep.selectMethod;
              selectedPaymentType = 'pay_now';
            });
          },
          onPayOnDeliverySelected: () {
            setState(() {
              currentStep = PaymentStep.selectMethod;
              selectedPaymentType = 'pay_on_delivery';
            });
          },
        );
      case PaymentStep.selectMethod:
        if (selectedPaymentType == 'pay_now') {
          return OnlinePaymentMethods(
            shopId: widget.shopId,
            orderTotal: widget.orderTotal,
            onPaymentSelected: _handlePaymentSelection,
          );
        } else {
          return DeliveryPaymentMethods(
            shopId: widget.shopId,
            orderTotal: widget.orderTotal,
            onPaymentSelected: _handlePaymentSelection,
          );
        }
    }
  }

  void _handlePaymentSelection(Map<String, dynamic> paymentData) {
    setState(() {
      selectedPaymentData = paymentData;
    });

    // For online payments, complete the selection immediately (process order)
    if (selectedPaymentType == 'pay_now') {
      _completePaymentSelection();
      return;
    }

    // For delivery payments, always complete selection immediately (just update UI)
    // The modal will close and update the payment method display
    if (selectedPaymentType == 'pay_on_delivery') {
      _completePaymentSelection();
      return;
    }
  }

  Widget _buildBottomAction() {
    // Only show confirm button for cash payments that require change input
    final shouldShowConfirmButton = selectedPaymentType == 'pay_on_delivery' &&
                                   selectedPaymentData != null &&
                                   selectedPaymentData!['requires_confirmation'] == true;

    if (!shouldShowConfirmButton) {
      return const SizedBox.shrink(); // Hide the button
    }

    return Container(
      decoration: BoxDecoration(
        color: AppStyle.white,
        border: Border(
          top: BorderSide(color: AppStyle.borderColor, width: 1),
        ),
      ),
      child: CustomButton(
        title: AppHelpers.getTranslation(TrKeys.confirm),
        onPressed: _completePaymentSelection,
      ),
    );
  }

  void _completePaymentSelection() {
    if (selectedPaymentData != null && widget.onPaymentSelected != null) {
      // First, trigger the callback to update the state
      widget.onPaymentSelected!(selectedPaymentData!);
      // Then, close the modal
      Navigator.of(context).pop();
    } else {
      Navigator.of(context).pop();
    }
  }
}
