import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pandoo_delivery/application/payment_methods/payment_state.dart';
import 'package:pandoo_delivery/application/payment_methods/payment_provider.dart';
import 'package:pandoo_delivery/infrastructure/services/app_helpers.dart';
import 'package:pandoo_delivery/infrastructure/services/tr_keys.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';
import 'package:pandoo_delivery/presentation/components/select_item.dart';
import 'package:pandoo_delivery/presentation/components/loading.dart';

class OnlinePaymentMethods extends ConsumerStatefulWidget {
  final int? shopId;
  final double? orderTotal;
  final Function(Map<String, dynamic>)? onPaymentSelected;

  const OnlinePaymentMethods({
    super.key,
    this.shopId,
    this.orderTotal,
    this.onPaymentSelected,
  });

  @override
  ConsumerState<OnlinePaymentMethods> createState() => _OnlinePaymentMethodsState();
}

class _OnlinePaymentMethodsState extends ConsumerState<OnlinePaymentMethods> {
  int selectedIndex = -1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(paymentProvider.notifier).fetchPayments(context, withOutCash: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(paymentProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppHelpers.getTranslation(TrKeys.selectPaymentMethod),
          style: AppStyle.interSemi(size: 16),
        ),
        16.verticalSpace,
        
        if (state.isPaymentsLoading)
          const Loading()
        else if (state.payments.isEmpty)
          _buildEmptyState()
        else
          _buildPaymentList(state),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: AppStyle.bgGrey,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppStyle.borderColor),
      ),
      child: Column(
        children: [
          Icon(
            Icons.payment_outlined,
            size: 48.r,
            color: AppStyle.textGrey,
          ),
          16.verticalSpace,
          Text(
            AppHelpers.getTranslation(TrKeys.noPaymentMethods),
            style: AppStyle.interSemi(size: 16, color: AppStyle.textGrey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentList(PaymentState state) {
    // Filter for "Pay Now" methods only (Wallet, Mercado Pago, Stripe, etc.)
    final onlinePayments = state.payments.where((payment) {
      final tag = payment.tag?.toLowerCase() ?? '';

      // EXCLUDE delivery payment methods (anything with "na entrega", "delivery", "cash")
      if (tag.contains('na entrega') ||
          tag.contains('delivery') ||
          tag.contains('cash') ||
          tag.contains('dinheiro')) {
        return false;
      }

      // INCLUDE only true online payment methods
      return tag.contains('wallet') ||
             tag.contains('carteira') ||
             tag.contains('mercado') ||
             tag.contains('pago') ||
             tag.contains('stripe') ||
             tag.contains('paypal') ||
             tag.contains('razorpay') ||
             tag.contains('paytm') ||
             tag.contains('flutterwave') ||
             tag == 'card' ||  // Only pure "card" (not "card_delivery")
             tag == 'pix';     // Only pure "pix" (not "pix_delivery")
    }).toList();

    if (onlinePayments.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: onlinePayments.asMap().entries.map((entry) {
        final index = entry.key;
        final payment = entry.value;
        
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          child: SelectItem(
            onTap: () {
              setState(() {
                selectedIndex = index;
              });
              
              // Immediately select online payment
              if (widget.onPaymentSelected != null) {
                widget.onPaymentSelected!({
                  'payment_method': payment.tag ?? '',
                  'payment_id': payment.id,
                  'payment_type': 'online',
                  'requires_confirmation': false,
                });
              }
            },
            isActive: selectedIndex == index,
            title: AppHelpers.getTranslation(payment.tag ?? ""),
          ),
        );
      }).toList(),
    );
  }
}
