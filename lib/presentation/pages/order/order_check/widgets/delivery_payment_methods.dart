import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pandoo_delivery/application/delivery_payment/delivery_payment_provider.dart';
import 'package:pandoo_delivery/application/delivery_payment/delivery_payment_state.dart';
import 'package:pandoo_delivery/infrastructure/services/app_helpers.dart';
import 'package:pandoo_delivery/infrastructure/services/tr_keys.dart';
import 'package:pandoo_delivery/presentation/components/buttons/custom_button.dart';
import 'package:pandoo_delivery/presentation/components/loading.dart';
import 'package:pandoo_delivery/presentation/components/title_icon.dart';
import 'package:pandoo_delivery/presentation/components/text_fields/custom_text_field.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';
import 'delivery_payment_method_item.dart';
import 'change_option_button.dart';
import 'package:pandoo_delivery/presentation/components/text_fields/currency_input_formatter.dart';

class DeliveryPaymentMethods extends ConsumerStatefulWidget {
  final int? shopId;
  final double? orderTotal;
  final Function(Map<String, dynamic>)? onPaymentSelected;

  const DeliveryPaymentMethods({
    super.key,
    this.shopId,
    this.orderTotal,
    this.onPaymentSelected,
  });

  @override
  ConsumerState<DeliveryPaymentMethods> createState() => _DeliveryPaymentMethodsState();
}

class _DeliveryPaymentMethodsState extends ConsumerState<DeliveryPaymentMethods> {
  final TextEditingController changeController = TextEditingController();
  final TextEditingController cashAmountController = TextEditingController();
  bool changeRequired = false;
  double cashPaymentAmount = 0.0;
  int selectedPaymentIndex = -1;
  final CurrencyInputFormatter currencyFormatter = CurrencyInputFormatter();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Always load default Brazilian methods first to ensure something is shown
      ref.read(deliveryPaymentProvider.notifier).loadDefaultMethods();

      // Set the order total in the state
      if (widget.orderTotal != null) {
        ref.read(deliveryPaymentProvider.notifier).setOrderTotal(widget.orderTotal!);
      }

      // Try to fetch from API if shopId is available (but don't let it override defaults with empty list)
      if (widget.shopId != null) {
        ref.read(deliveryPaymentProvider.notifier)
            .fetchDeliveryPaymentMethods(context, widget.shopId!);
      }
    });
  }

  @override
  void dispose() {
    changeController.dispose();
    cashAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(deliveryPaymentProvider);
    final notifier = ref.read(deliveryPaymentProvider.notifier);

    return Container(
      decoration: BoxDecoration(
        color: AppStyle.bgGrey.withOpacity(0.96),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      width: double.infinity,
      child: state.isLoading
          ? const Loading()
          : SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Drag Handle
                    Container(
                      height: 4.h,
                      width: 48.w,
                      decoration: BoxDecoration(
                        color: AppStyle.dragElement,
                        borderRadius: BorderRadius.all(Radius.circular(40.r)),
                      ),
                    ),

                    14.verticalSpace,

                    // Header
                    TitleAndIcon(
                      title: AppHelpers.getTranslation(TrKeys.selectDeliveryPayment),
                    ),

                    24.verticalSpace,

                    // Payment Methods List
                    if (state.methods.isNotEmpty)
                      ...state.methods.asMap().entries.map((entry) {
                        final index = entry.key;
                        final method = entry.value;

                        return DeliveryPaymentMethodItem(
                          method: method,
                          isSelected: state.selectedIndex == index,
                          onTap: () {
                            notifier.selectPaymentMethod(index);

                            // For non-cash payments, immediately close modal and update UI
                            // Check the method directly, not the current state
                            final isCashMethod = method.tag == 'cash' ||
                                                method.tag == 'cash_delivery' ||
                                                method.tag?.contains('cash') == true;

                            if (!isCashMethod) {
                              // Non-cash payments (PIX, Credit Card, Debit Card) auto-close modal
                              // Use a small delay to ensure state is updated, then trigger selection
                              Future.delayed(const Duration(milliseconds: 100), () {
                                _selectPaymentMethodByIndex(index);
                              });
                            }
                          },
                        );
                      })
                    else
                      Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 32.h),
                          child: Text(
                            AppHelpers.getTranslation(TrKeys.noDeliveryPaymentMethods),
                            style: AppStyle.interSemi(
                              size: 16,
                              color: AppStyle.textGrey,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),

                    // Change Amount Input (shown only for cash)
                    if (_isCashSelected(state)) ...[
                      16.verticalSpace,
                      _buildChangeSection(state, notifier),
                    ],

                    20.verticalSpace,

                    // Confirm Button
                    CustomButton(
                      title: AppHelpers.getTranslation(TrKeys.confirm),
                      onPressed: _isValidSelection(state) ? () => _confirmSelection(state) : null,
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildChangeSection(DeliveryPaymentState state, notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppHelpers.getTranslation(TrKeys.needChange),
          style: AppStyle.interNormal(size: 16),
        ),

        16.verticalSpace,

        // Change Required Toggle
        Row(
          children: [
            Expanded(
              child: ChangeOptionButton(
                title: AppHelpers.getTranslation(TrKeys.yes),
                isSelected: state.changeRequired,
                onTap: () {
                  notifier.setChangeRequired(true);
                },
              ),
            ),

            12.horizontalSpace,

            Expanded(
              child: ChangeOptionButton(
                title: AppHelpers.getTranslation(TrKeys.no),
                isSelected: !state.changeRequired,
                onTap: () {
                  notifier.setChangeRequired(false);
                  changeController.clear();
                  cashAmountController.clear();
                },
              ),
            ),
          ],
        ),

        // Cash Payment Amount Input
        if (state.changeRequired) ...[
          16.verticalSpace,
          Text(
            AppHelpers.getTranslation(TrKeys.cashPaymentAmount),
            style: AppStyle.interNormal(size: 14),
          ),
          4.verticalSpace,
          Text(
            AppHelpers.getTranslation(TrKeys.cashPaymentAmountHint),
            style: AppStyle.interRegular(size: 12, color: AppStyle.textGrey),
          ),
          8.verticalSpace,
          CustomTextField(
            controller: cashAmountController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [currencyFormatter],
            hint: state.orderTotal > 0
                ? 'Ex: ${AppHelpers.numberFormat(number: state.orderTotal + 50)}'
                : 'Ex: R\$ 250,00',
            onChanged: (value) {
              final amount = CurrencyInputFormatter.getNumericValue(value);
              // Debounce updates to prevent excessive rebuilds
              Future.microtask(() {
                notifier.setCashPaymentAmount(amount);
              });
            },
          ),

          // Validation Error
          if (state.changeRequired && state.cashPaymentAmount > 0 && !state.isValidCashAmount) ...[
            8.verticalSpace,
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppStyle.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppStyle.red.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: AppStyle.red, size: 16.r),
                  8.horizontalSpace,
                  Expanded(
                    child: Text(
                      AppHelpers.getTranslation(TrKeys.cashAmountMustBeGreater),
                      style: AppStyle.interRegular(size: 12, color: AppStyle.red),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Change Summary
          if (state.changeRequired && state.isValidCashAmount && state.calculatedChange > 0) ...[
            16.verticalSpace,
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppStyle.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: AppStyle.primary.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppHelpers.getTranslation(TrKeys.changeSummary),
                    style: AppStyle.interSemi(size: 14, color: AppStyle.primary),
                  ),
                  8.verticalSpace,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        AppHelpers.getTranslation(TrKeys.changeToReturn),
                        style: AppStyle.interRegular(size: 14, color: AppStyle.black),
                      ),
                      Text(
                        AppHelpers.numberFormat(number: state.calculatedChange),
                        style: AppStyle.interSemi(size: 16, color: AppStyle.primary),
                      ),
                    ],
                  ),
                  4.verticalSpace,
                  Text(
                    AppHelpers.getTranslation(TrKeys.driverWillReturn),
                    style: AppStyle.interRegular(size: 12, color: AppStyle.textGrey),
                  ),
                ],
              ),
            ),
          ],
        ],
      ],
    );
  }

  bool _isCashSelected(DeliveryPaymentState state) {
    if (state.selectedIndex == -1 || state.methods.isEmpty) return false;
    final tag = state.methods[state.selectedIndex].tag;
    return tag == 'cash' || tag == 'cash_delivery' || tag?.contains('cash') == true;
  }

  bool _isValidSelection(DeliveryPaymentState state) {
    if (state.selectedIndex == -1) return false;
    if (_isCashSelected(state) && state.changeRequired) {
      return state.isValidCashAmount && state.cashPaymentAmount > 0;
    }
    return true;
  }

  void _selectPaymentMethod(DeliveryPaymentState state) {
    final selectedMethod = state.methods[state.selectedIndex];
    final isCash = _isCashSelected(state);

    final paymentData = {
      'payment_method': selectedMethod.tag,
      'payment_type': 'delivery',
      'payment_id': null, // Delivery payments don't have payment IDs
      'requires_confirmation': false, // For UI update only, no confirmation needed
      'change_required': false,
      'change_amount': null,
      'cash_payment_amount': null,
      'order_total': state.orderTotal,
      'payment_notes': null,
      'method_name': selectedMethod.name,
      'method_description': selectedMethod.description,
      'is_cash': isCash,
    };

    widget.onPaymentSelected?.call(paymentData);
    // Don't call context.maybePop() here - let the parent modal handle navigation
  }

  void _selectPaymentMethodByIndex(int index) {
    final state = ref.read(deliveryPaymentProvider);
    final selectedMethod = state.methods[index];
    final isCash = selectedMethod.tag == 'cash' ||
                   selectedMethod.tag == 'cash_delivery' ||
                   selectedMethod.tag?.contains('cash') == true;

    final paymentData = {
      'payment_method': selectedMethod.tag,
      'payment_type': 'delivery',
      'payment_id': null, // Delivery payments don't have payment IDs
      'requires_confirmation': false, // For UI update only, no confirmation needed
      'change_required': false,
      'change_amount': null,
      'cash_payment_amount': null,
      'order_total': state.orderTotal,
      'payment_notes': null,
      'method_name': selectedMethod.name,
      'method_description': selectedMethod.description,
      'is_cash': isCash,
    };

    widget.onPaymentSelected?.call(paymentData);
    // Don't call context.maybePop() here - let the parent modal handle navigation
  }

  void _confirmSelection(DeliveryPaymentState state) {
    final selectedMethod = state.methods[state.selectedIndex];
    final isCash = _isCashSelected(state);

    final paymentData = {
      'payment_method': selectedMethod.tag,
      'payment_type': 'delivery',
      'payment_id': null, // Delivery payments don't have payment IDs
      'requires_confirmation': isCash && selectedMethod.supportsChange,
      'change_required': isCash ? state.changeRequired : false,
      'change_amount': isCash ? state.calculatedChange : null,
      'cash_payment_amount': isCash ? state.cashPaymentAmount : null,
      'order_total': state.orderTotal,
      'payment_notes': isCash && state.changeRequired
          ? 'Cliente pagará com R\$ ${AppHelpers.numberFormat(number: state.cashPaymentAmount)}. Troco: R\$ ${AppHelpers.numberFormat(number: state.calculatedChange)}'
          : null,
      'method_name': selectedMethod.name,
      'method_description': selectedMethod.description,
      'is_cash': isCash,
    };

    widget.onPaymentSelected?.call(paymentData);
    // Don't call context.maybePop() here - let the parent modal handle navigation
  }
}
