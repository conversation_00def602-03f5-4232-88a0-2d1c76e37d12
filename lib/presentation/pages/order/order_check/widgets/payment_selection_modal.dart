import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pandoo_delivery/infrastructure/services/app_helpers.dart';
import 'package:pandoo_delivery/infrastructure/services/tr_keys.dart';
import 'package:pandoo_delivery/presentation/components/title_icon.dart';
import 'package:pandoo_delivery/presentation/theme/theme.dart';
import 'payment_type_selector.dart';

class PaymentSelectionModal extends StatelessWidget {
  final int? shopId;
  final double? orderTotal;
  final Function(Map<String, dynamic>)? onPaymentSelected;

  const PaymentSelectionModal({
    super.key,
    this.shopId,
    this.orderTotal,
    this.onPaymentSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppStyle.bgGrey.withOpacity(0.96),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      width: double.infinity,
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Drag Handle
              Container(
                height: 4.h,
                width: 48.w,
                decoration: BoxDecoration(
                  color: AppStyle.dragElement,
                  borderRadius: BorderRadius.all(Radius.circular(40.r)),
                ),
              ),

              14.verticalSpace,

              // Header
              TitleAndIcon(
                title: AppHelpers.getTranslation(TrKeys.selectPaymentMethod),
              ),

              24.verticalSpace,

              // Payment Type Selector
              PaymentTypeSelector(
                shopId: shopId,
                orderTotal: orderTotal,
                onPaymentSelected: onPaymentSelected,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
