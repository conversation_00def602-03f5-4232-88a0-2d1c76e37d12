# 🌐 app.pandoo.delivery Domain Setup Guide

## ✅ **Changes Applied:**

### 1. **App Configuration Updated:**
- ✅ `lib/app_constants.dart`: webUrl changed to `https://app.pandoo.delivery/`
- ✅ `android/app/src/main/AndroidManifest.xml`: host changed to `app.pandoo.delivery`
- ✅ `ios/Runner/Info.plist`: associated domain changed to `app.pandoo.delivery`

### 2. **Domain Verification Files Ready:**
- ✅ `assetlinks.json`: Ready for Android App Links
- ✅ `apple-app-site-association`: Ready for iOS Universal Links

## 🚀 **Domain Setup Steps:**

### **Step 1: DNS Configuration**
Configure your DNS to point `app.pandoo.delivery` to your server:

```dns
# A Record
app.pandoo.delivery → YOUR_SERVER_IP

# Or CNAME if using CDN
app.pandoo.delivery → your-cdn-domain.com
```

### **Step 2: SSL Certificate**
Ensure SSL certificate covers `app.pandoo.delivery`:

```bash
# If using Let's Encrypt
certbot certonly --webroot -w /var/www/html -d app.pandoo.delivery

# Or update existing certificate
certbot certonly --webroot -w /var/www/html -d api.pandoo.delivery -d app.pandoo.delivery
```

### **Step 3: Web Server Configuration**

#### **For Nginx:**
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name app.pandoo.delivery;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    root /var/www/app-pandoo;
    index index.html;
    
    # Serve verification files
    location /.well-known/assetlinks.json {
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin *;
        try_files $uri =404;
    }
    
    location /.well-known/apple-app-site-association {
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin *;
        try_files $uri =404;
    }
    
    # Deep link routes - redirect to app or show fallback
    location ~ ^/(shop|group)/ {
        try_files $uri $uri/ @fallback;
    }
    
    location @fallback {
        # Redirect to app stores if app not installed
        return 302 https://play.google.com/store/apps/details?id=com.pandoo.user;
    }
}
```

#### **For Apache:**
```apache
<VirtualHost *:80>
    ServerName app.pandoo.delivery
    Redirect permanent / https://app.pandoo.delivery/
</VirtualHost>

<VirtualHost *:443>
    ServerName app.pandoo.delivery
    DocumentRoot /var/www/app-pandoo
    
    SSLEngine on
    SSLCertificateFile /path/to/ssl/cert.pem
    SSLCertificateKeyFile /path/to/ssl/private.key
    
    # Serve verification files
    <Location "/.well-known/">
        Header set Content-Type "application/json"
        Header set Access-Control-Allow-Origin "*"
    </Location>
    
    # Deep link fallback
    RewriteEngine On
    RewriteRule ^/(shop|group)/ /fallback.html [L]
</VirtualHost>
```

### **Step 4: Upload Verification Files**
Upload these files to `app.pandoo.delivery`:

```bash
# Create directory structure
mkdir -p /var/www/app-pandoo/.well-known/

# Copy verification files
cp assetlinks.json /var/www/app-pandoo/.well-known/
cp apple-app-site-association /var/www/app-pandoo/.well-known/

# Set permissions
chmod 644 /var/www/app-pandoo/.well-known/*
```

### **Step 5: Create Fallback Pages**
Create user-friendly fallback pages for when the app isn't installed:

#### **index.html:**
```html
<!DOCTYPE html>
<html>
<head>
    <title>Pandoo Delivery</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
</head>
<body>
    <h1>Pandoo Delivery</h1>
    <p>Download our app:</p>
    <a href="https://play.google.com/store/apps/details?id=com.pandoo.user">
        Download for Android
    </a>
    <a href="https://apps.apple.com/app/your-app-id">
        Download for iOS
    </a>
</body>
</html>
```

## 🧪 **Testing Steps:**

### **1. Verify Domain Access:**
```bash
curl -I https://app.pandoo.delivery/
# Should return 200 OK

curl -I https://app.pandoo.delivery/.well-known/assetlinks.json
# Should return 200 OK with Content-Type: application/json

curl -I https://app.pandoo.delivery/.well-known/apple-app-site-association
# Should return 200 OK with Content-Type: application/json
```

### **2. Test Deep Links:**
```bash
# Test URLs that should work:
https://app.pandoo.delivery/shop/123
https://app.pandoo.delivery/shop/123?product=abc
https://app.pandoo.delivery/group/123?g=456&o=789
```

### **3. Verify App Links (Android):**
```bash
adb shell am start -W -a android.intent.action.VIEW -d "https://app.pandoo.delivery/shop/123" com.pandoo.user
```

### **4. Test Universal Links (iOS):**
Open Safari and navigate to: `https://app.pandoo.delivery/shop/123`

## 📋 **Checklist:**

- [ ] 🔄 DNS configured for app.pandoo.delivery
- [ ] 🔄 SSL certificate installed
- [ ] 🔄 Web server configured
- [ ] 🔄 Verification files uploaded
- [ ] 🔄 Fallback pages created
- [ ] 🔄 Domain accessibility tested
- [ ] 🔄 Deep links tested on Android
- [ ] 🔄 Universal links tested on iOS

## 🎯 **Benefits After Setup:**

1. **Professional URLs**: `app.pandoo.delivery/shop/123`
2. **Better UX**: Fallback to app stores if app not installed
3. **SEO Friendly**: Can add meta tags for social sharing
4. **Separation**: API and app domains serve different purposes
5. **Scalability**: Easy to add web app later

## ⚠️ **Important Notes:**

1. **Update sharing**: All shared links will now use app.pandoo.delivery
2. **Test thoroughly**: Verify both Android and iOS deep linking
3. **Monitor**: Check server logs for any issues
4. **Backup**: Keep api.pandoo.delivery configuration as backup

**Your app is now configured to use the professional app.pandoo.delivery domain for all deep linking!**
