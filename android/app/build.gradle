plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id 'com.google.gms.google-services'
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def keystoreProperties = new Properties ()
def keystorePropertiesFile = rootProject.file ("key.properties")
if (keystorePropertiesFile.exists()) {keystoreProperties.load(new FileInputStream(keystorePropertiesFile))}
def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0.0'
}

android {
    namespace "com.pandoo.user"
    compileSdk 35
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.pandoo.user"
        minSdkVersion 23
        multiDexEnabled true
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties["keyAlias"]
            keyPassword keystoreProperties["keyPassword"]
            storeFile keystoreProperties["storeFile"] ?file(keystoreProperties["storeFile"]) : null
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source '../..'
}

// Task para gerar fingerprints do certificado
task generateFingerprints {
    doLast {
        println "\n🔐 FINGERPRINTS DO CERTIFICADO DEBUG:"
        println "======================================"

        def debugKeystore = System.getProperty("user.home") + "/.android/debug.keystore"
        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
            debugKeystore = System.getProperty("user.home") + "\\.android\\debug.keystore"
        }

        println "📍 Keystore: $debugKeystore"

        if (new File(debugKeystore).exists()) {
            try {
                def proc = ["keytool", "-list", "-v", "-keystore", debugKeystore,
                           "-alias", "androiddebugkey", "-storepass", "android", "-keypass", "android"].execute()
                proc.waitFor()

                def output = proc.text
                def sha256 = output.find(/SHA256: ([A-F0-9:]+)/) { match, sha -> sha }
                def sha1 = output.find(/SHA1: ([A-F0-9:]+)/) { match, sha -> sha }

                if (sha256) {
                    println "✅ SHA256: $sha256"
                    println "\n📋 Para assetlinks.json use:"
                    println "\"$sha256\""
                } else {
                    println "❌ SHA256 não encontrado na saída do keytool"
                }

                if (sha1) {
                    println "✅ SHA1: $sha1"
                }

            } catch (Exception e) {
                println "❌ Erro ao executar keytool: ${e.message}"
                println "💡 Tente executar o script PowerShell: generate_fingerprints.ps1"
            }
        } else {
            println "❌ Debug keystore não encontrado em: $debugKeystore"
            println "💡 Execute 'flutter run' primeiro para gerar o debug keystore"
        }

        println "\n📖 Próximos passos:"
        println "1. Copie o SHA256 acima"
        println "2. Substitua no assetlinks.json.template"
        println "3. Upload para https://app.pandoo.delivery/.well-known/assetlinks.json"
    }
}

dependencies {
    implementation platform('com.google.firebase:firebase-bom:32.7.2')
    implementation 'com.google.firebase:firebase-analytics-ktx'

}
