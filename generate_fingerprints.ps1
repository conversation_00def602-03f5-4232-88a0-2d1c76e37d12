# Script PowerShell para gerar fingerprints do Android
Write-Host "🔍 Procurando keytool..." -ForegroundColor Yellow

# Possíveis localizações do keytool
$keytoolPaths = @(
    "${env:ProgramFiles}\Android\Android Studio\jbr\bin\keytool.exe",
    "${env:ProgramFiles}\Android\Android Studio\jre\bin\keytool.exe",
    "${env:ProgramFiles}\Java\jdk*\bin\keytool.exe",
    "${env:ProgramFiles(x86)}\Android\Android Studio\jbr\bin\keytool.exe",
    "${env:ProgramFiles(x86)}\Android\Android Studio\jre\bin\keytool.exe"
)

$keytoolFound = $null

foreach ($path in $keytoolPaths) {
    $expandedPath = Get-ChildItem $path -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($expandedPath -and (Test-Path $expandedPath.FullName)) {
        $keytoolFound = $expandedPath.FullName
        Write-Host "✅ Keytool encontrado em: $keytoolFound" -ForegroundColor Green
        break
    }
}

if (-not $keytoolFound) {
    Write-Host "❌ Keytool não encontrado. Procurando em todo o sistema..." -ForegroundColor Red
    $keytoolFound = Get-ChildItem -Path "C:\" -Recurse -Name "keytool.exe" -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($keytoolFound) {
        Write-Host "✅ Keytool encontrado em: $keytoolFound" -ForegroundColor Green
    } else {
        Write-Host "❌ Keytool não encontrado no sistema." -ForegroundColor Red
        Write-Host "📥 Instale o Android Studio ou Java JDK primeiro." -ForegroundColor Yellow
        exit 1
    }
}

# Verificar se o debug keystore existe
$debugKeystore = "$env:USERPROFILE\.android\debug.keystore"
Write-Host "🔍 Verificando debug keystore em: $debugKeystore" -ForegroundColor Yellow

if (-not (Test-Path $debugKeystore)) {
    Write-Host "❌ Debug keystore não encontrado." -ForegroundColor Red
    Write-Host "📱 Execute um app Flutter em modo debug primeiro para gerar o keystore." -ForegroundColor Yellow
    Write-Host "💡 Comando: flutter run" -ForegroundColor Cyan
    exit 1
}

Write-Host "✅ Debug keystore encontrado!" -ForegroundColor Green
Write-Host "🔐 Gerando fingerprints..." -ForegroundColor Yellow

# Executar keytool para gerar fingerprints
try {
    & $keytoolFound -list -v -keystore $debugKeystore -alias androiddebugkey -storepass android -keypass android
    Write-Host "`n✅ Fingerprints geradas com sucesso!" -ForegroundColor Green
    Write-Host "📋 Copie o valor SHA256 para usar no assetlinks.json" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Erro ao executar keytool: $_" -ForegroundColor Red
}

Write-Host "`n📖 Próximos passos:" -ForegroundColor Yellow
Write-Host "1. Copie o valor SHA256 da saída acima" -ForegroundColor White
Write-Host "2. Substitua no arquivo assetlinks.json.template" -ForegroundColor White
Write-Host "3. Faça upload para https://app.pandoo.delivery/.well-known/assetlinks.json" -ForegroundColor White
